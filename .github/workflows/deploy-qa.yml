name: Magento deploy to QA

on:
  push:
    branches: qa
  workflow_dispatch:

env:
  environment: "qa"

concurrency:
  group: deploy-qa
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: comave-staging
    environment: qa

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install rsync
        run: sudo apt-get update && sudo apt-get install -y rsync

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY_QA }}
          known_hosts: ${{ secrets.KNOWN_HOSTS }}

      - name: Generate timestamp
        id: timestamp
        run: |
          timestamp=$(date +%Y%m%d%H%M%S)
          echo "timestamp=${timestamp}" >> $GITHUB_OUTPUT

      - name: Create release directory on Main Node
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_INSTANCE_USER }}@${{ secrets.EC2_INSTANCE_QA_1_IP }} "
            mkdir -p ${release_dir}"

      - name: Sync code to Main Node
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          rsync -avz -e "ssh -o StrictHostKeyChecking=no" ./ ${{ secrets.EC2_INSTANCE_USER }}@${{ secrets.EC2_INSTANCE_QA_1_IP }}:${release_dir}

      - name: Deploy to Main Node (EC2 Instance 1)
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_INSTANCE_USER }}@${{ secrets.EC2_INSTANCE_QA_1_IP }} "
            cd ${release_dir} &&
            export TIMESTAMP=${{ steps.timestamp.outputs.timestamp }} &&
            export ENVIRONMENT=${{ env.environment }} &&
            bash -s" < ./aws-deploy.sh
