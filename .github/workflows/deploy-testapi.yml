name: Magento deploy to TESTAPI

on:
  push:
    branches: testapi
  workflow_dispatch:

env:
  environment: "testapi"

concurrency:
  group: deploy-testapi
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: comave-staging
    environment: testapi

    steps:
      - name: Checkout code on branch testapi
        uses: actions/checkout@v3

      - name: Install rsync
        run: sudo apt-get update && sudo apt-get install -y rsync

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY_TESTAPI }}
          known_hosts: ${{ secrets.KNOWN_HOSTS }}

      - name: Generate timestamp
        id: timestamp
        run: |
          timestamp=$(date +%Y%m%d%H%M%S)
          echo "timestamp=${timestamp}" >> $GITHUB_OUTPUT

      - name: Create release directory on Main Node for testapi
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_INSTANCE_USER }}@${{ secrets.EC2_INSTANCE_TESTAPI_1_IP }} "
            mkdir -p ${release_dir}"

      - name: Sync code to Main Node
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          rsync -avz -e "ssh -o StrictHostKeyChecking=no" ./ ${{ secrets.EC2_INSTANCE_USER }}@${{ secrets.EC2_INSTANCE_TESTAPI_1_IP }}:${release_dir}

      - name: Deploy to Main Node (EC2 Instance 1)
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_INSTANCE_USER }}@${{ secrets.EC2_INSTANCE_TESTAPI_1_IP }} "
            cd ${release_dir} &&
            export TIMESTAMP=${{ steps.timestamp.outputs.timestamp }} &&
            export ENVIRONMENT=${{ env.environment }} &&
            bash -s" < ./aws-deploy.sh
