name: Magento deploy to PRODUCTION

on:
  release:
    types: [published]

env:
  environment: "prod"

concurrency:
  group: deploy-production
  cancel-in-progress: false

jobs:
  deploy:
    runs-on: comave-production
    environment: production

    steps:
      - name: Checkout code on latest TAG
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
          fetch-depth: 0

      - name: Install rsync
        run: sudo apt-get update && sudo apt-get install -y rsync

      - name: Fail if tag name is invalid
        run: |
          if [[ ! "${GITHUB_REF}" =~ ^refs/tags/v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "Invalid tag name: ${GITHUB_REF}. Tag name must follow the pattern 'v<MAJOR>.<MINOR>.<PATCH>'"
            exit 1
          fi

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY_PROD }}
          known_hosts: ${{ vars.KNOWN_HOSTS }}

      - name: Generate timestamp
        id: timestamp
        run: |
          timestamp=$(date +%Y%m%d%H%M%S)
          echo "timestamp=${timestamp}" >> $GITHUB_OUTPUT

      - name: Create release directory on Main Node
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_INSTANCE_USER }}@${{ vars.EC2_INSTANCE_PROD_1_IP }} "
            mkdir -p ${release_dir}"

      - name: Sync code to Main Node
        run: |
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          rsync -avz -e "ssh -o StrictHostKeyChecking=no" ./ ${{ secrets.EC2_INSTANCE_USER }}@${{ vars.EC2_INSTANCE_PROD_1_IP }}:${release_dir}

      - name: Deploy to Main Node (EC2 Instance 1)
        run: |
          echo "DEPLOY_ENV value: $DEPLOY_ENV"
          release_dir="/var/www/app/releases/${{ steps.timestamp.outputs.timestamp }}"
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_INSTANCE_USER }}@${{ vars.EC2_INSTANCE_PROD_1_IP }} "
            cd ${release_dir} &&
            export TIMESTAMP=${{ steps.timestamp.outputs.timestamp }} &&
            export ENVIRONMENT=${{ env.environment }} &&
            bash -s" < ./aws-deploy.sh
