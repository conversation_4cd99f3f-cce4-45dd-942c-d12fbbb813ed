<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/** @var \Magento\Framework\Escaper $escaper */
/** @var $block \Webkul\Marketplace\Block\Account\Editprofile */
$viewModel = $block->getViewModel();
$lowStockViewModel = $block->getLowStockViewModel();
$helper = $viewModel->getHelper();
$partner = $block->getPersistentData();
if (empty($partner)) {
    $partner=$helper->getSeller();
}
$_helper = $this->helper(\Comave\LixApiConnector\Helper\Data::class);

$sellerID = $helper->getCustomerId();

$profile_hint_status = $helper->getProfileHintStatus();
$currencySymbol = $helper->getCurrencySymbol();
$formPostUrl = $block->getUrl(
    'marketplace/account/editProfilePost',
    ["_secure" => $block->getRequest()->isSecure()]
);
$questIcon = $block->getViewFileUrl('Webkul_Marketplace::images/quest.png');
$deleteIcon = $block->getViewFileUrl('Webkul_Marketplace::images/deleteIcon.png');
$countryflag = $block->getViewFileUrl('Webkul_Marketplace::images/country/countryflags/');
?>

<div class="container-fluid">
    <div class="row sub-info-dashboard-one">
        <div class="profile-completion">
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: <?php echo $_helper->getProfileCompletionPercentageSeller($sellerID); ?>%;"></div>
            </div>
        </div>
        <div class="status">
          <span>Your profile is <?php echo $_helper->getProfileCompletionPercentageSeller($sellerID); ?>% complete</span>
        </div>
    </div>
</div>

<form action="<?= $escaper->escapeUrl($formPostUrl) ?>"
    enctype="multipart/form-data"
    method="post" data-role="form-profile-validate"
    data-mage-init='{"validation":{}}'>
    <div class="wk-mp-design">
        <fieldset class="fieldset info wk-mp-fieldset">
            <div data-mage-init='{"formButtonAction": {}}'
                class="wk-mp-page-title legend" id="wk-mp-editprofile-form">
                <span><?= /* @noEscape */ __('Edit Profile Information') ?></span>
                <button class="button wk-mp-btn"
                    title="<?= /* @noEscape */ __('Save Profile') ?>"
                    type="submit" id="save-btn">
                    <span><span><?= /* @noEscape */ __('Save Profile') ?></span></span>
                </button>
            </div>
            <?= $block->getBlockHtml('seller.formkey')?>
            <?= $block->getBlockHtml('formkey')?>

            <div class="field profile">

                <h2><?= /* @noEscape */ __('Contact information') ?></h2>

                <label><?= /* @noEscape */ __('Contact Number') ?> </label>
                <?php
                if ($profile_hint_status && $helper->getProfileHintCn()) {?>
                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
                    class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProfileHintCn()) ?>"/>
                    <?php
                } ?>
                <div class="control">
                    <?php
                    $placeholderTxt = __(
                        'Enter Contact Number with country code ex: (123) 456-7890'
                    );
                    ?>
                    <input type="text" id="contact_number"
                    name="contact_number"
                    value="<?= $escaper->escapeHtml($partner['contact_number']); ?>"
                    title="<?= /* @noEscape */ __('Enter Contact Number') ?>"
                    class="input-text"
                    placeholder="<?= /* @noEscape */ $placeholderTxt ?>"/>
                </div>
                <div class="field profile">
                    <label><?= /* @noEscape */ __('Email') ?></label>
                    <div class="control">
                        <input type="email"
                               name="email"
                               id="email"
                               class="input-text"
                               placeholder="<?= /* @noEscape */ __('Enter Email') ?>"
                               value="<?= $escaper->escapeHtml($partner['email'] ?? '') ?>" />
                    </div>
                </div>
                <div class="field profile">
                    <label><?= /* @noEscape */ __('Business URL') ?></label>
                    <div class="control">
                        <input type="url"
                               name="business_url"
                               id="business_url"
                               class="input-text"
                               placeholder="https://example.com"
                               value="<?= $escaper->escapeHtml($partner['business_url'] ?? '') ?>" />
                    </div>
                </div>

                <h2><?= /* @noEscape */ __('Business details') ?></h2>
                <div class="field profile">
                    <label for="shoptitle"><?= /* @noEscape */ __('Business Name') ?></label>
                    <?php
                    if ($profile_hint_status && $helper->getProfileHintShop()) {?>
                        <img src="<?= $escaper->escapeUrl($questIcon); ?>"
                             class='questimg'
                             title="<?= $escaper->escapeHtml($helper->getProfileHintShop()) ?>"/>
                        <?php
                    } ?>
                    <div class="control">
                        <input type="text" id="shop_title"
                               name="shop_title"
                               value="<?= $escaper->escapeHtml($partner['shop_title']); ?>"
                               title="<?= /* @noEscape */ __('Business Name')?>"
                               placeholder="<?= /* @noEscape */ __('Enter Business Name') ?>"
                               class="input-text" />
                    </div>
                </div>
                <div class="field profile">
                    <label><?= /* @noEscape */ __('Owner Name') ?></label>
                    <div class="control">
                        <input type="text"
                               name="owner_name"
                               id="owner_name"
                               class="input-text"
                               placeholder="<?= /* @noEscape */ __('Enter Owner Name') ?>"
                               value="<?= $escaper->escapeHtml($partner['owner_name'] ?? '') ?>" />
                    </div>
                </div>

                <div class="fieldset-sublegend"><?= __('Business Address') ?></div>

                <div class="field profile required">
                    <label for="business_country"><?= __('Country') ?></label>
                    <div class="control">
                        <select name="business_country"
                                id="business_country"
                                class="select required-entry">
                            <option value="" selected="selected" disabled="disabled">
                                <?= /* @noEscape */ __('Select Country')?>
                            </option>
                            <?php foreach ($block->getCountryOptionArray() as $country) {?>
                                <option <?php
                                if ($country['value']!='') {
                                    $cSelect = $partner['business_country']==$country['value']?"selected='selected'":""; ?>
                                    value="<?= $escaper->escapeHtml($country['value']); ?>" <?= /* @noEscape */ $cSelect?>>
                                    <?= $escaper->escapeHtml($country['label']);?>
                                    </option>
                                    <?php
                                }
                            } ?>
                        </select>
                    </div>
                </div>

                <div class="field profile required">
                    <label for="business_city"><?= __('City') ?></label>
                    <div class="control">
                        <input type="text"
                               name="business_city"
                               id="business_city"
                               class="input-text required-entry"
                               value="<?= $escaper->escapeHtml($partner['business_city'] ?? '') ?>"/>
                    </div>
                </div>

                <div class="field profile required">
                    <label for="business_street"><?= __('Street') ?></label>
                    <div class="control">
                        <input type="text"
                               name="business_street"
                               id="business_street"
                               class="input-text required-entry"
                               value="<?= $escaper->escapeHtml($partner['business_street'] ?? '') ?>"/>
                    </div>
                </div>

                <div class="field profile required">
                    <label for="business_postcode"><?= __('Postal Code') ?></label>
                    <div class="control">
                        <input type="text"
                               name="business_postcode"
                               id="business_postcode"
                               class="input-text required-entry validate-alphanum"
                               value="<?= $escaper->escapeHtml($partner['business_postcode'] ?? '') ?>"/>
                    </div>
                </div>

                <div class="fieldset-sublegend"><?= __('Shipping From Address') ?></div>

                <div class="field profile">
                    <label class="label">
                        <input type="hidden"
                               name="ship_same_as_business"
                               value="0" />

                        <input type="checkbox"
                               id="ship_same_as_business"
                               name="ship_same_as_business"
                               value="1"
                            <?= ($partner['ship_same_as_business'] ?? 0) ? 'checked' : '' ?> />

                        <?= __('Same as Business Address') ?>
                    </label>
                </div>


                <div class="field profile required ship-block">
                    <label for="ship_country"><?= __('Country') ?></label>
                    <div class="control">
                        <select name="ship_country"
                                id="ship_country"
                                class="select">
                            <option value="" selected="selected" disabled="disabled">
                                <?= /* @noEscape */ __('Select Country')?>
                            </option>
                            <?php foreach ($block->getCountryOptionArray() as $country) {?>
                                <option <?php
                                if ($country['value']!='') {
                                    $cSelect = $partner['ship_country']==$country['value']?"selected='selected'":""; ?>
                                    value="<?= $escaper->escapeHtml($country['value']); ?>" <?= /* @noEscape */ $cSelect?>>
                                    <?= $escaper->escapeHtml($country['label']);?>
                                    </option>
                                    <?php
                                }
                            } ?>
                        </select>
                    </div>
                </div>

                <div class="field profile required ship-block">
                    <label for="ship_city"><?= __('City') ?></label>
                    <div class="control">
                        <input type="text" name="ship_city"
                               id="ship_city" class="input-text"
                               value="<?= $escaper->escapeHtml($partner['ship_city'] ?? '') ?>"/>
                    </div>
                </div>

                <div class="field profile required ship-block">
                    <label for="ship_street"><?= __('Street') ?></label>
                    <div class="control">
                        <input type="text" name="ship_street"
                               id="ship_street" class="input-text"
                               value="<?= $escaper->escapeHtml($partner['ship_street'] ?? '') ?>"/>
                    </div>
                </div>

                <div class="field profile required ship-block">
                    <label for="ship_postcode"><?= __('Postal Code') ?></label>
                    <div class="control">
                        <input type="text" name="ship_postcode"
                               id="ship_postcode"
                               class="input-text validate-alphanum"
                               value="<?= $escaper->escapeHtml($partner['ship_postcode'] ?? '') ?>"/>
                    </div>
                </div>

                <h2><?= /* @noEscape */ __('Business classification') ?></h2>

                <div class="field profile required">
                    <label for="vendor_group"><?= __('Vendor Group') ?></label>
                    <div class="control">
                        <select name="vendor_group"
                                id="vendor_group"
                                class="select required-entry">
                            <option value="" disabled <?= empty($partner['vendor_group']) ? 'selected' : '' ?>>
                                <?= __('Select Group') ?>
                            </option>
                            <option value="Retail" <?= ($partner['vendor_group'] ?? '') == 'Retail' ? 'selected' : '' ?>>
                                <?= __('Retail') ?>
                            </option>
                            <option value="Sports" <?= ($partner['vendor_group'] ?? '') == 'Sports' ? 'selected' : '' ?>>
                                <?= __('Sports') ?>
                            </option>
                            <option value="Club" <?= ($partner['vendor_group'] ?? '') == 'Club' ? 'selected' : '' ?>>
                                <?= __('Club') ?>
                            </option>
                        </select>
                    </div>
                </div>

                <div class="field profile required">
                    <label for="business_registration_number"><?= __('Business Registration Number') ?></label>
                    <div class="control">
                        <input type="text"
                               name="business_registration_number"
                               id="business_registration_number"
                               class="input-text"
                               placeholder="<?= /* @noEscape */ __('Enter Business Registration Number') ?>"
                               value="<?= $escaper->escapeHtml($partner['business_registration_number'] ?? '') ?>" />
                    </div>
                </div>

            </div>
            <div class="field profile">
                <label><?= /* @noEscape */ __('VAT Number') ?> </label>
                <?php
                if ($profile_hint_status && $helper->getProfileHintTax()) {?>
                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
                    class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProfileHintTax()) ?>"/>
                    <?php
                } ?>
                <div class="control">
                    <input type="text"
                    id="taxvat" name="taxvat"
                    value="<?= $escaper->escapeHtml($partner['taxvat']); ?>"
                    title="<?= /* @noEscape */ __('Enter Tax or VAT number') ?>"
                    class="input-text"
                    placeholder="<?= /* @noEscape */ __('Enter Tax or VAT number') ?>"/>
                </div>
            </div>


            <h2><?= __('Authentication') ?></h2>

            <div class="field profile">
                <button type="button"
                        id="change-password-toggle"
                        class="button action secondary">
                    <span><?= __('Change Password') ?></span>
                </button>
            </div>

            <div id="change-password-form" style="display: none;">
                <form action="<?= $escaper->escapeUrl($block->getUrl('marketplace/account/changePassword')) ?>"
                      method="post"
                      data-mage-init='{"validation":{}}'>
                    <?= $block->getBlockHtml('formkey') ?>

                    <div class="field profile required">
                        <label for="current_password"><?= __('Current Password') ?></label>
                        <div class="control">
                            <input type="password"
                                   name="current_password"
                                   id="current_password"
                                   class="input-text required-entry"
                                   autocomplete="current-password" />
                        </div>
                    </div>

                    <div class="field profile required">
                        <label for="password"><?= __('New Password') ?></label>
                        <div class="control">
                            <input type="password"
                                   name="password"
                                   id="password"
                                   class="input-text required-entry validate-customer-password"
                                   autocomplete="new-password" />
                        </div>
                    </div>

                    <div class="field profile required">
                        <label for="password_confirmation"><?= __('Confirm New Password') ?></label>
                        <div class="control">
                            <input type="password"
                                   name="password_confirmation"
                                   id="password_confirmation"
                                   class="input-text required-entry validate-cpassword"
                                   data-validate="{equalTo:'#password'}"
                                   autocomplete="new-password" />
                        </div>
                    </div>

                    <div class="actions-toolbar">
                        <div class="primary">
                            <button type="submit" class="button action primary">
                                <span><?= __('Save New Password') ?></span>
                            </button>
                        </div>
                        <div class="secondary">
                            <button type="button" id="cancel-password-change" class="button action secondary">
                                <span><?= __('Cancel') ?></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <?php
            if ($helper->getActiveColorPicker()) {?>
                <div class="field profile">
                    <label for="background_width"><?= /* @noEscape */ __('Theme : Background Color') ?> </label>
                    <?php
                    if ($profile_hint_status && $helper->getProfileHintBc()) {?>
                        <img src="<?= $escaper->escapeUrl($questIcon); ?>"
                        class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProfileHintBc()) ?>"/>
                        <?php
                    } ?>
                    <div class="control">
                        <input type="text" id="background_width"
                        name="background_width"
                        value="<?= $escaper->escapeHtml($partner['background_width']); ?>"
                        title="background_width" class="input-text" />
                        <span class="color-pick"
                        data-role="color-pick"
                        data-mage-init='{
                            "colorPickerFunction":{
                                "spanBackgroundColor" : "<?= $escaper
                                ->escapeHtml($partner['background_width'])?>",
                                "getActiveColorPickerStatus" : "<?= $escaper
                                ->escapeHtml($helper->getActiveColorPicker())?>",
                                "backgroundWidthSelector" : "#background_width"
                            }
                        }'>
                        </span>
                    </div>
                </div>
                <?php
            } ?>


            <?php
            if ($helper->getSellerPolicyApproval()) {?>
                <div class="field profile">
                    <label><?= /* @noEscape */ __('Return Policy') ?></label>
                    <?php
                    if ($profile_hint_status && $helper->getProfileHintReturnPolicy()) {?>
                        <img src="<?= $escaper->escapeUrl($questIcon); ?>"
                        class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProfileHintReturnPolicy()) ?>"/>
                        <?php
                    } ?>
                    <div class="control">
                        <textarea type="text" id="return_policy"
                        name="return_policy"
                        title="<?= /* @noEscape */ __('Return Policy') ?>"
                        class="input-text compdesi" >
                            <?= /* @noEscape */ $partner['return_policy']; ?>
                        </textarea>
                        <?php if ($helper->isWysiwygEnabled()): ?>
                            <script>
                                require([
                                    "jquery",
                                    "mage/translate",
                                    "mage/adminhtml/events",
                                    "mage/adminhtml/wysiwyg/tiny_mce/setup"
                                ], function(jQuery) {
                                    wysiwygCompanyDescription = new wysiwygSetup("return_policy", {
                                        "width" : "100%",
                                        "height" : "200px",
                                        "plugins" : [{"name":"image"}],
                                        "tinymce4" : {
                                            "toolbar":"formatselect | bold italic underline | "+
                                            "alignleft aligncenter alignright |" +
                                            "bullist numlist |"+
                                            "link table charmap","plugins":"advlist "+
                                            "autolink lists link charmap media noneditable table "+
                                            "contextmenu paste code help table",
                                        },
                                        files_browser_window_url: "<?= $escaper
                                        ->escapeUrl($block->getWysiwygUrl());?>"
                                    });
                                    wysiwygCompanyDescription.setup("exact");
                                });
                            </script>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="field profile">
                    <label><?= /* @noEscape */ __('Shipping Policy') ?></label>
                    <?php
                    if ($profile_hint_status && $helper->getProfileHintShippingPolicy()) {?>
                        <img src="<?= $escaper->escapeUrl($questIcon); ?>"
                        class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProfileHintShippingPolicy()) ?>"/>
                        <?php
                    } ?>
                    <div class="control">
                        <textarea type="text" id="shipping_policy"
                        name="shipping_policy" title="<?= /* @noEscape */ __('Shipping Policy') ?>"
                        class="input-text compdesi" >
                            <?= /* @noEscape */ $partner['shipping_policy']; ?>
                        </textarea>
                        <?php if ($helper->isWysiwygEnabled()): ?>
                            <script>
                                require([
                                    "jquery",
                                    "mage/translate",
                                    "mage/adminhtml/events",
                                    "mage/adminhtml/wysiwyg/tiny_mce/setup"
                                ], function(jQuery) {
                                    wysiwygCompanyDescription = new wysiwygSetup("shipping_policy", {
                                        "width" : "100%",
                                        "height" : "200px",
                                        "plugins" : [{"name":"image"}],
                                        "tinymce4" : {
                                            "toolbar":"formatselect | bold italic underline | "+
                                            "alignleft aligncenter alignright |" +
                                            "bullist numlist |"+
                                            "link table charmap","plugins":"advlist "+
                                            "autolink lists link charmap media noneditable table "+
                                            "contextmenu paste code help table",
                                        },
                                        files_browser_window_url: "<?= $escaper
                                        ->escapeUrl($block->getWysiwygUrl());?>"
                                    });
                                    wysiwygCompanyDescription.setup("exact");
                                });
                            </script>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="field profile">
                    <label><?= /* @noEscape */ __('Privacy Policy') ?></label>
                    <div class="control">
                        <textarea type="text" id="privacy_policy"
                        name="privacy_policy"
                        title="<?= /* @noEscape */ __('Privacy Policy') ?>"
                        class="input-text compdesi" >
                            <?= /* @noEscape */ $partner['privacy_policy']; ?>
                        </textarea>
                         <?php if ($helper->isWysiwygEnabled()): ?>
                            <script>
                                require([
                                    "jquery",
                                    "mage/translate",
                                    "mage/adminhtml/events",
                                    "mage/adminhtml/wysiwyg/tiny_mce/setup"
                                ], function(jQuery) {
                                    wysiwygCompanyDescription = new wysiwygSetup("privacy_policy", {
                                        "width" : "100%",
                                        "height" : "200px",
                                        "plugins" : [{"name":"image"}],
                                        "tinymce4" : {
                                            "toolbar":"formatselect | bold italic underline | "+
                                            "alignleft aligncenter alignright |" +
                                            "bullist numlist |"+
                                            "link table charmap","plugins":"advlist "+
                                            "autolink lists link charmap media noneditable table "+
                                            "contextmenu paste code help table",
                                        ,
                                        files_browser_window_url: "<?= $escaper
                                        ->escapeUrl($block->getWysiwygUrl());?>"
                                    });
                                    wysiwygCompanyDescription.setup("exact");
                                });
                            </script>
                        <?php endif; ?>
                    </div>
                </div>
                <?php
            } ?>

        </fieldset>
    </div>
</form>
<?php
$profileUrl = 'marketplace/seller/profile/shop/'.$partner['shop_url'];
$collectionUrl = 'marketplace/seller/collection/shop/'.$partner['shop_url'];
$feedbackUrl = 'marketplace/seller/feedback/shop/'.$partner['shop_url'];
$locationUrl = 'marketplace/seller/location/shop/'.$partner['shop_url'];
$policyUrl = 'marketplace/seller/policy/shop/'.$partner['shop_url'];

if ($helper->getUrlRewrite()) { ?>
    <br/><br/>
    <?php
    $rewriteUrl = $block->getUrl(
        'marketplace/account/rewriteUrlPost',
        ["_secure" => $block->getRequest()->isSecure()]
    );
    ?>
    <form action="<?= $escaper->escapeUrl($rewriteUrl) ?>"
        enctype="multipart/form-data" method="post"
        data-role="form-urlrewrite-validate"
        data-mage-init='{"validation":{}}'>
        <div class="page-title" style="width:100%;display:inline-block;">
            <h2 style="float:left;">
                <?= /* @noEscape */ __('Manage Your Shop Url') ?>
            </h2>
            <button class="button wk-mp-btn"
                title="<?= /* @noEscape */ __('Set Your New Shop Url') ?>"
                type="submit" style="float:right;">
                <span><span><?= /* @noEscape */ __('Save Url') ?></span></span>
            </button>
        </div>
        <?= $block->getBlockHtml('seller.formkey')?>
        <?= $block->getBlockHtml('formkey')?>
        <div class="wk-mp-design">
            <fieldset class="fieldset info wk-mp-fieldset">
                <legend class="legend"><span><?= /* @noEscape */ __('Edit Your Shop Url') ?></span></legend>
                <div class="field">
                    <label><?= /* @noEscape */ __('Profile Page Target Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        value="<?= /* @noEscape */ $profileUrl ?>" readonly/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Profile Page Request Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        name="profile_request_url" id="profile_request_url"
                        value="<?= /* @noEscape */ $helper->getRewriteUrlPath($profileUrl) ?>"/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Collection Page Target Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        value="<?= /* @noEscape */ $collectionUrl ?>" readonly/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Collection Page Request Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        name="collection_request_url" id="collection_request_url"
                        value="<?= /* @noEscape */ $helper->getRewriteUrlPath($collectionUrl) ?>"/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Review Page Target Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        value="<?= /* @noEscape */ $feedbackUrl; ?>" readonly/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Review Page Request Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        name="review_request_url" id="review_request_url"
                        value="<?= /* @noEscape */ $helper->getRewriteUrlPath($feedbackUrl) ?>"/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Location Page Target Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        value="<?= /* @noEscape */ $locationUrl; ?>" readonly/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Location Page Request Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        name="location_request_url" id="location_request_url"
                        value="<?= /* @noEscape */ $helper->getRewriteUrlPath($locationUrl) ?>"/>
                    </div>
                </div>
                <div class="field">
                    <label><?= /* @noEscape */ __('Privacy Policy Page Request Url Path') ?></label>
                    <div class="control">
                        <input type="text" class="input-text"
                        name="policy_request_url" id="policy_request_url"
                        value="<?= /* @noEscape */ $helper->getRewriteUrlPath($policyUrl) ?>"/>
                    </div>
                </div>
            </fieldset>
        </div>
    </form>
    <?php
}
?>
<br/><br/>
<br/><br/>
<?php if ($helper->getMinOrderSettings()): ?>
    <?php
    $saveMinOrderUrl = $block->getUrl(
        'marketplace/account/saveMinOrder',
        ["_secure" => $block->getRequest()->isSecure()]
    );
    ?>
    <form action="<?= $escaper->escapeUrl($saveMinOrderUrl) ?>"
    method="post" data-mage-init='{"validation":{}}'>
        <div class="wk-mp-page-title page-title">
            <h2><?= /* @noEscape */ __('Set Minimum Order Amount') ?></h2>
            <button class="button wk-mp-btn"
            title="<?= /* @noEscape */ __('Save') ?>" type="submit">
                <span><span><?= /* @noEscape */ __('Save') ?></span></span>
            </button>
        </div>
        <?= $block->getBlockHtml('seller.formkey')?>
        <?= $block->getBlockHtml('formkey')?>
        <div class="wk-mp-design">
            <fieldset class="fieldset info wk-mp-fieldset">
                <div class="field required">
                    <label class="label">
                    <?= /* @noEscape */ __('Minimum Amount') ?>
                    <b><?= /* @noEscape */ " (".$currencySymbol.")"; ?></b>
                    </label>
                    <div class="control">
                        <input type="text"
                        class="input-text validate-number required-entry validate-greater-than-zero"
                        name="min_order_amount" title="Minimum Order Amount"
                        value ="<?= $escaper->escapeHtml($block->getMinimumOrderValue())?>"
                        id="min_order_amount" />
                    </div>
                </div>
            </fieldset>
        </div>
    </form>
    <br/><br/>
<?php endif; ?>

<?php
$lowStockUrl = $block->getUrl(
    'marketplace/account/savelowstockthreshold',
    ["_secure" => $block->getRequest()->isSecure()]
);
?>
<form action="<?= $escaper->escapeUrl($lowStockUrl) ?>"
      method="post" data-mage-init='{"validation":{}}'>
    <div class="wk-mp-page-title page-title">
        <h2><?= /* @noEscape */ __('Overwrite Admin Low Stock Threshold') ?></h2>
        <button class="button wk-mp-btn"
                title="<?= /* @noEscape */ __('Save') ?>" type="submit">
            <span><span><?= /* @noEscape */ __('Save') ?></span></span>
        </button>
    </div>
    <?= $block->getBlockHtml('seller.formkey')?>
    <?= $block->getBlockHtml('formkey')?>
    <div class="wk-mp-design">
        <fieldset class="fieldset info wk-mp-fieldset">
            <div class="field required">
                <label class="label">
                    <?= /* @noEscape */ __('Low Stock Threshold Value') ?>
                </label>
                <div class="control">
                    <input type="text"
                           class="input-text validate-number required-entry validate-greater-than-zero"
                           name="seller_low_stock_threshold" title="Low Stock Threshold Value"
                           value ="<?= $escaper->escapeHtml($lowStockViewModel->getLowStockThresholdValue())?>"
                           id="seller_low_stock_threshold" />
                </div>
            </div>
        </fieldset>
    </div>
</form>
<br/><br/>


<?= $block->getChildHtml(); ?>

<div class="buttons-set">
    <p class="back-link">
        <a href="javascript:;"
            onclick="javascript: window.history.back();"
            class="left">&laquo; <?= /* @noEscape */ __('Back') ?></a>
    </p>
</div>
<?php
$formData = [
    'countryPicSelector' => '#country-pic',
    'countryImgPrevSelector' => '.country_img_prev',
    'countryLatitudeSelector' => '#country-latitude',
    'countryLongitudeSelector' => '#country-longitude',
    'countryImgPrev' => $block->getViewFileUrl("Webkul_Marketplace::images/country/countryflags/"),
    'backgroundWidthSelector' => '#background_width',
    'logoPicSelector' => '#logo-pic',
    'bannerPicSelector' => '#banner-pic',
    'leftButtonSelector' => '.left',
    'buttonsSetLastSelector' => '.buttons-set:last',
    'inputTextSelector' => '.input-text',
    'profileimageSetSpanSelector' => '.profileimage-set span',
    'bannerSelector' => '.wk-banner',
    'bannerDeleteAjaxUrl' => $block->getUrl(
        'marketplace/account/deleteSellerBanner',
        ['_secure' => $block->getRequest()->isSecure()]
    ),
    'setimageSelector' => '.setimage',
    'profileImageDeleteSelector' => '.wk-profileimagedelete img',
    'logoImageSetSpanSelector' => '.logoimage-set span',
    'logoSelector' => '.wk-logo',
    'logoDeleteAjaxUrl' => $block->getUrl(
        'marketplace/account/deleteSellerLogo',
        ['_secure' => $block->getRequest()->isSecure()]
    ),
    'logoImageDeleteSelector' => '.wk-logoimagedelete img'
];
$serializedFormData = $viewModel->getJsonHelper()->jsonEncode($formData);
?>

<script type="text/x-magento-init">
    {
        "*": {
            "editSellerProfile": <?= /* @noEscape */ $serializedFormData; ?>
        }
    }
</script>
<script type="text/javascript">
    require(['jquery'], function ($) {
        'use strict';

        // Shipping address toggle functionality
        var $same   = $('#ship_same_as_business'),
            $block  = $('.ship-block'),
            fields  = {
                country:  ['#business_country',  '#ship_country'],
                city:     ['#business_city',     '#ship_city'],
                street:   ['#business_street',   '#ship_street'],
                post:     ['#business_postcode', '#ship_postcode']
            };

        function toggleShipping() {
            var same = $same.prop('checked');

            $block.toggle(!same);

            $block.find('input, select')
                .toggleClass('required-entry', !same);

            if (same) {
                $.each(fields, function (_, pair) {
                    $(pair[1]).val($(pair[0]).val());
                });
            }
        }

        toggleShipping();
        $same.on('change', toggleShipping);

        $('#business_country').on('change', function () {
            if ($same.prop('checked')) {
                $('#ship_country').val(this.value);
            }
        });

        // Change password form toggle functionality
        var $passwordToggle = $('#change-password-toggle'),
            $passwordForm = $('#change-password-form'),
            $cancelButton = $('#cancel-password-change');

        $passwordToggle.on('click', function () {
            $passwordForm.slideToggle();
            $(this).text(
                $passwordForm.is(':visible') ?
                '<?= __('Hide Change Password') ?>' :
                '<?= __('Change Password') ?>'
            );
        });

        $cancelButton.on('click', function () {
            $passwordForm.slideUp();
            $passwordToggle.text('<?= __('Change Password') ?>');
            // Clear form fields
            $passwordForm.find('input[type="password"]').val('');
        });
    });
</script>