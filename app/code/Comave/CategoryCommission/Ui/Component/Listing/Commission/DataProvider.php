<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Ui\Component\Listing\Commission;

use Comave\CategoryCommission\Model\ResourceModel\Commission\CollectionFactory;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Magento\Framework\App\RequestInterface;

/**
 * Class DataProvider
 */
class DataProvider extends AbstractDataProvider {

    /**
     * DataProvider constructor.
     * @param CollectionFactory $collectionFactory
     * @param string $name
     * @param string primaryFieldName
     * @param string $requestFieldName
     * @param array $meta
     * @param array $data
     * @param RequestInterface $request
     */
    public function __construct(
        private CollectionFactory $collectionFactory,
                          $name,
                          $primaryFieldName,
                          $requestFieldName,
        private RequestInterface $request,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct(
            $name,
            $primaryFieldName,
            $requestFieldName,
            $meta,
            $data
        );
        $this->collection = $collectionFactory->create();
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        $collection = $this->getCollection();
        $storeId = $this->request->getParam('current_store_id');

        if ($storeId) {
            $collection->addFieldToFilter('store_id', $storeId);
        }

        return $collection->toArray();
    }
}
