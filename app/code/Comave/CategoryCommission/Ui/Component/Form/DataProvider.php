<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Ui\Component\Form;

use Comave\CategoryCommission\Model\ResourceModel\Commission\CollectionFactory;
use Magento\Ui\DataProvider\AbstractDataProvider;

/**
 * Class ModalDataProvider
 */
class DataProvider extends AbstractDataProvider
{
    /**
     * @param CollectionFactory $collectionFactory
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        private CollectionFactory $collectionFactory,
        string $name,
        string $primaryFieldName,
        string $requestFieldName,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct(
            $name,
            $primaryFieldName,
            $requestFieldName,
            $meta,
            $data
        );
        $this->collection = $collectionFactory->create();
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData(): array
    {
        return [
        ];
    }
}
