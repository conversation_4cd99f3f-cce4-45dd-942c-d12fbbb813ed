<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="category_commission" resource="default" engine="innodb" comment="Category Commission">
		<column xsi:type="smallint" name="entity_id" padding="12" unsigned="true" nullable="false" identity="true" comment="ID"/>
        <column xsi:type="int" name="store_id" padding="6" nullable="false" comment="Type"/>
        <column xsi:type="text" name="category_name" nullable="true" comment="Category Name"/>
        <column xsi:type="int" name="category_id" unsigned="true" nullable="false" comment="Type"/>
        <column xsi:type="int" name="type" padding="2" nullable="false" comment="Type"/>
        <column xsi:type="int" name="value" padding="5" nullable="false" comment="Value"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_CATEGORY_COMMISSION_STORE_CATEGORY_UNIQUE">
            <column name="category_id"/>
            <column name="store_id"/>
        </constraint>
    </table>
</schema>
