<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add
            id="Comave_CategoryCommission::comave_commission_import_export"
            title="Category Commission"
            module="Comave_CategoryCommission"
            sortOrder="60"
            parent="Magento_Catalog::catalog"
            resource="Comave_CategoryCommission::import_export"
        />
        <add
            id="Comave_CategoryCommission::import_export"
            title="Import/Export"
            module="Comave_CategoryCommission"
            sortOrder="10"
            action="commission/index"
            resource="Comave_CategoryCommission::import_export"
            parent="Comave_CategoryCommission::comave_commission_import_export"
        />
    </menu>
</config>
