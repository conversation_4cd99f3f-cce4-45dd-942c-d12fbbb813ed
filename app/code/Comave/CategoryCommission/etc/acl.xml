<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Catalog::catalog" title="Catalog" translate="title" sortOrder="30">
                    <resource id="Magento_Catalog::catalog_inventory" title="Inventory" translate="title" sortOrder="10">
                         <resource id="Comave_CategoryCommission::import_export" title="Import Export" translate="title" sortOrder="30"/>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
