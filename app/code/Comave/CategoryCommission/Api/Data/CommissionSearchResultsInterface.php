<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Api\Data;

interface CommissionSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Commission list.
     * @return \Comave\CategoryCommission\Api\Data\CommissionInterface[]
     */
    public function getItems();

    /**
     * Set Commission .
     * @param \Comave\CategoryCommission\Api\Data\CommissionInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

