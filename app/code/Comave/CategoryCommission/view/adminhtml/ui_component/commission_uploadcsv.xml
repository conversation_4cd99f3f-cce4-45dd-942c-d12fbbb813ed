<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">commission_uploadcsv.commission_uploadcsv_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Upload</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <namespace>commission_uploadcsv</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>commission_uploadcsv.commission_uploadcsv_data_source</dep>
        </deps>
        <buttons>
            <button name="Save" class="Comave\CategoryCommission\Block\Adminhtml\Buttons\SaveImport">
            </button>
        </buttons>
    </settings>
    <dataSource name="commission_uploadcsv_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <dataProvider class="Comave\CategoryCommission\Ui\Component\Form\DataProvider" name="commission_uploadcsv_data_source">
            <settings>
                <requestFieldName>scope</requestFieldName>
                <primaryFieldName>scope</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="fields" sortOrder="30">
        <settings>
            <label translate="true">Upload CSV commissions file</label>
        </settings>
        <field name="commission_file" formElement="fileUploader">
            <settings>
                <label translate="true">Commission File</label>
            </settings>
            <formElements>
                <fileUploader>
                    <settings>
                        <uploaderConfig>
                            <param xsi:type="string" name="url">commission/import/uploadcsv</param>
                        </uploaderConfig>
                    </settings>
                </fileUploader>
            </formElements>
        </field>
    </fieldset>
</form>
