<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Controller\Adminhtml\Index;

use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Backend\App\Action;
use Comave\CategoryCommission\Api\CommissionRepositoryInterface;
use Comave\CategoryCommission\Api\Data\CommissionInterface;

/**
 * Class InlineEdit
 */
class InlineEdit extends Action
{

    /**
     * @param Context $context
     * @param CommissionRepositoryInterface $commissionRepository
     * @param JsonFactory $jsonFactory
     */
    public function __construct(
        Context $context,
        private    CommissionRepositoryInterface $commissionRepository,
        private    JsonFactory $jsonFactory
    ) {
        parent::__construct($context);
    }

    public function execute()
    {
        $resultJson = $this->jsonFactory->create();
        $error = false;
        $messages = [];
        $postItems = $this->getRequest()->getParam('items', []);
        if (!($this->getRequest()->getParam('isAjax') && count($postItems))) {

            $messages[] = __('Please correct the data sent.');
            $error = true;
        }
        foreach (array_keys($postItems) as $commissionId) {
            $commission = $this->commissionRepository->getById($commissionId);
            try {
                $commission->setData(array_merge($commission->getData(), $postItems[$commissionId]));
                $this->commissionRepository->save($commission);
            } catch (\Exception $e) {
                $messages[] = $this->getErrorWithCommissionId(
                    $commission,
                    __($e->getMessage())
                );
                $error = true;
            }
        }

        return $resultJson->setData([
            'messages' => $messages,
            'error' => $error
        ]);
    }

    /**
     * @param CommissionInterface $commission
     * @param $errorText
     * @return string
     */
    protected function getErrorWithCommissionId(CommissionInterface $commission, $errorText)
    {
        return '[Commission ID: ' . $commission->getId() . '] ' . $errorText;
    }
}
