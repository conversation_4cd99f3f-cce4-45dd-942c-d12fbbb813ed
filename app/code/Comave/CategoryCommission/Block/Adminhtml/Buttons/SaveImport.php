<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Block\Adminhtml\Buttons;

use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;
use Magento\Backend\Block\Widget\Context;

class SaveImport implements ButtonProviderInterface
{

    /**
     * @param Context $context
     */
    public function __construct(
        private    Context $context
    ) {
    }

    /**
     * @return array
     */
    public function getButtonData(): array
    {
        return [
            'label' => __('Save Import'),
            'on_click' => sprintf("location.href = '%s';", $this->getRedirectUrl()),
            'class' => 'primary',
            'sort_order' => 10
        ];
    }

    /**
     * @return string
     */
    public function getRedirectUrl(): string
    {
        return $this->getUrl('commission/index/index');
    }

    /**
     * @param string $route
     * @param array $params
     * @return string
     */
    public function getUrl($route = '', $params = []): string
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }
}
