<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Model\Commission;

use Comave\CategoryCommission\Model\ResourceModel\Commission\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Ui\DataProvider\Modifier\PoolInterface;
use Magento\Ui\DataProvider\ModifierPoolDataProvider;
use Magento\Framework\App\RequestInterface;
/**
 * Class DataProvider
 */
class DataProvider extends ModifierPoolDataProvider
{

    /**
     * @var CollectionFactory
     */
    protected $collection;

    /**
     * @var DataPersistorInterface
     */
    private $dataPersistor;

    /**
     * @var array
     */
    private $loadedData;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * DataProvider constructor.
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $commissionCollectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param RequestInterface $request
     * @param array $meta
     * @param array $data
     * @param PoolInterface|null $pool
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $commissionCollectionFactory,
        DataPersistorInterface $dataPersistor,
        RequestInterface $request,
        array $meta = [],
        array $data = [],
        PoolInterface $pool = null
    ) {
        $this->collection = $commissionCollectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->request = $request;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data, $pool);
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        $items = $this->collection->getItems();
        foreach ($items as $commission) {
            $this->loadedData[$commission->getEntityId()] = $commission->getData();
        }

        $data = $this->dataPersistor->get('commission');
        if (!empty($data)) {
            $commission = $this->collection->getNewEmptyItem();
            $commission->setData($data);
            $this->loadedData[$commission->getId()] = $commission->getData();
            $this->dataPersistor->clear('commission');
        }
        return $this->loadedData;
    }
}
