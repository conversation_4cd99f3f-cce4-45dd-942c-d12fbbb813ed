<?php
declare(strict_types=1);
namespace Comave\CategoryCommission\Plugin\Catalog\Model\CategoryRepository;

use Comave\CategoryCommission\Api\CommissionRepositoryInterface;

/**
 * Class Save
 */
class Save
{

    /**
     * @var \Magento\Catalog\Api\Data\CategoryInterface
     */
    private $currentCategory;

    /**
     * Save constructor.
     * @param CommissionRepositoryInterface $commissionRepository
     */
    public function __construct(
        private CommissionRepositoryInterface $commissionRepository
    ) {
    }

    /**
     * @param \Magento\Catalog\Api\CategoryRepositoryInterface $subject
     * @param \Magento\Catalog\Api\Data\CategoryInterface $entity
     */
    public function beforeSave(\Magento\Catalog\Api\CategoryRepositoryInterface $subject, \Magento\Catalog\Api\Data\CategoryInterface $entity)
    {
        $this->currentCategory = $entity;
    }

    /**
     * @param \Magento\Catalog\Api\CategoryRepositoryInterface $subject
     * @param \Magento\Catalog\Api\Data\CategoryInterface $entity
     * @return \Magento\Catalog\Api\Data\CategoryInterface
     */
    public function afterSave(\Magento\Catalog\Api\CategoryRepositoryInterface $subject, \Magento\Catalog\Api\Data\CategoryInterface $entity)
    {
        if ($this->currentCategory == null) {
            return $entity;
        }
        $extensionAttributes = $this->currentCategory->getExtensionAttributes();
        /** get current extension attributes from entity **/
        if ($extensionAttributes && is_array($extensionAttributes->getCommission())) {
            $commisions = $extensionAttributes->getCommission();
            $oldCommissions = $entity->getExtensionAttributes()->getCommission();
            if (is_array($commisions)) {
                foreach ($oldCommissions as $oldCommission) {
                    $this->commissionRepository->delete($oldCommission);
                }
                foreach ($commisions as $commision) {
                    if (!$commision->getCategoryId()) {
                        $commision->setCategoryId($entity->getId());
                    }
                    $this->commissionRepository->save($commision);
                }
            }
        }

        $this->currentCategory = null;

        return $entity;
    }
}
