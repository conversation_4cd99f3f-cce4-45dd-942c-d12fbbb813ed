<?php

declare(strict_types=1);

namespace Comave\CategoryCommission\Setup\Patch;

use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Webkul\MpAdvancedCommission\Setup\Patch\Data\EavCommissionAttribute;
use Comave\SellerPayouts\Setup\Patch\Data\CategoryCommissionAttribute;

class RemoveCategoryCommissionAttribute implements DataPatchInterface
{
    /**
     * @param EavSetupFactory $eavSetupFactory
     * @param ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly ModuleDataSetupInterface $moduleDataSetup,
    ) {
    }

    /**
     * @return string[]
     */
    public static function getDependencies(): array
    {
        return [
            EavCommissionAttribute::class,
            CategoryCommissionAttribute::class
        ];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $eavSetup->removeAttribute(
            \Magento\Catalog\Model\Category::ENTITY,
            'commission_for_admin'
        );
    }
}
