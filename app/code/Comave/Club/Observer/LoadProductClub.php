<?php
declare(strict_types=1);

namespace Comave\Club\Observer;

use Magento\Framework\Event\ObserverInterface;
use Comave\Club\Model\ResourceModel\ClubProduct\CollectionFactory as ClubProductCollectionFactory;

class LoadProductClub implements ObserverInterface
{
    public function __construct(
        private readonly ClubProductCollectionFactory $clubProductCollectionFactory
    ) {
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(\Magento\Framework\Event\Observer $observer): void
    {
        $product = $observer->getProduct();
        if ($product === null || !$product->getId()) {
            return;
        }

        $clubProductCollection = $this->clubProductCollectionFactory->create()
            ->addFieldToSelect('club_id')
            ->addFieldToFilter('product_id', $product->getId());
        $clubIds = $clubProductCollection->getColumnValues('club_id');

        if (!empty($clubIds)) {
            $product->setData('product_club', implode(',', $clubIds));
        }
    }
}
