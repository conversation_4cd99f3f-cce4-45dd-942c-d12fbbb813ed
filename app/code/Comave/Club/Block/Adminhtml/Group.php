<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Adminhtml;

/**
 * Adminhtml cms pages content block
 */
class Group extends \Magento\Backend\Block\Widget\Grid\Container
{
    /**
     * Block constructor
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_controller = 'adminhtml_clubgroup';
        $this->_blockGroup = 'Comave_Club';
        $this->_headerText = __('Manage Club Groups');

        parent::_construct();

        if ($this->_isAllowedAction('Comave_Club::group_edit')) {
            $this->buttonList->update('add', 'label', __('Add New Club Group'));
        } else {
            $this->buttonList->remove('add');
        }
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->_authorization->isAllowed($resourceId);
    }
}
