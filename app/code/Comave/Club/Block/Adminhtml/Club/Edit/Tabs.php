<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Club\Block\Adminhtml\Club\Edit;

use Comave\Club\Block\Adminhtml\Club\Edit\Tab\Main;

class Tabs extends \Magento\Backend\Block\Widget\Tabs
{
    /**
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('page_tabs');
        $this->setDestElementId('edit_form');
        $this->setTitle(__('Club Information'));
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _prepareLayout()
    {
        $this->addTab(
            'general',
            [
                'label' => __('Club Information'),
                'content' => $this->getLayout()->createBlock(Main::class)->toHtml(),
            ]
        )->addTab(
            'products',
            [
                'label' => __('Products'),
                'url' => $this->getUrl('comave_club/*/products', ['_current' => true]),
                'class' => 'ajax',
            ]
        );
    }
}
