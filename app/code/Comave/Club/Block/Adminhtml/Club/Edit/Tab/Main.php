<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Adminhtml\Club\Edit\Tab;

class Main extends \Magento\Backend\Block\Widget\Form\Generic implements \Magento\Backend\Block\Widget\Tab\TabInterface
{
	/**
     * @var \Magento\Store\Model\System\Store
     */
    protected $_systemStore;

    /**
     * @var \Magento\Cms\Model\Wysiwyg\Config
     */
    protected $_wysiwygConfig;

    /**
     * @var \Comave\Club\Helper\Data
     */
    protected $_viewHelper;

    /**
     * @param \Magento\Backend\Block\Template\Context $context       
     * @param \Magento\Framework\Registry             $registry      
     * @param \Magento\Framework\Data\FormFactory     $formFactory   
     * @param \Magento\Store\Model\System\Store       $systemStore   
     * @param \Magento\Cms\Model\Wysiwyg\Config       $wysiwygConfig 
     * @param \Comave\Club\Helper\Data                  $viewHelper
     * @param array                                   $data          
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Store\Model\System\Store $systemStore,
        \Magento\Cms\Model\Wysiwyg\Config $wysiwygConfig,
        \Comave\Club\Helper\Data $viewHelper,
        array $data = []
    ) {
        $this->_viewHelper = $viewHelper;
        $this->_systemStore = $systemStore;
        $this->_wysiwygConfig = $wysiwygConfig;
        parent::__construct($context, $registry, $formFactory, $data);
    }


    /**
     * Prepare form
     *
     * @return $this
     */
    protected function _prepareForm() {
    	/** @var $model \Comave\Club\Model\Club */
    	$model = $this->_coreRegistry->registry('comave_club');
        
        $wysiwygConfig = $this->_wysiwygConfig->getConfig(['tab_id' => $this->getTabId()]);
    	/**
    	 * Checking if user have permission to save information
    	 */
    	if($this->_isAllowedAction('Comave_Club::club_edit')){
    		$isElementDisabled = false;
    	}else {
    		$isElementDisabled = true;
    	}
    	/** @var \Magento\Framework\Data\Form $form */
    	$form = $this->_formFactory->create();

    	$form->setHtmlIdPrefix('club_');

    	$fieldset = $form->addFieldset('base_fieldset', ['legend' => __('Club Information')]);


    	if ($model->getId()) {
    		$fieldset->addField('club_id', 'hidden', ['name' => 'club_id']);
    	}

    	$fieldset->addField(
    		'name',
    		'text',
    		[
                'name'     => 'name',
                'label'    => __('Club Name'),
                'title'    => __('Club Name'),
                'required' => true,
                'disabled' => $isElementDisabled
    		]
    		);
        
		$fieldset->addField(
            'subtitle',
            'text',
            [
                'name'     => 'subtitle',
                'label'    => __('Subtitle Text'),
                'title'    => __('Subtitle Text'),
                'disabled' => $isElementDisabled
            ]
        );
		
		$fieldset->addField(
    		'uniqueid',
    		'text',
    		[
                'name'     => 'uniqueid',
                'label'    => __('Club Unique Identifier'),
                'title'    => __('Club Unique Identifier'),
                'required' => true,
                'disabled' => $isElementDisabled
    		]
    		);
		
		$fieldset->addField(
    		'orgid',
    		'text',
    		[
                'name'     => 'orgid',
                'label'    => __('Club Organisation ID'),
                'title'    => __('Club Organisation ID'),
                'required' => true,
                'disabled' => $isElementDisabled
    		]
    		);
        $fieldset->addField(
            'club_prefix',
            'text',
            [
                'name'     => 'club_prefix',
                'label'    => __('Club Prefix'),
                'title'    => __('Club Prefix'),
                'disabled' => $isElementDisabled                
            ]
            );
        $fieldset->addField(
            'club_count',
            'text',
            [
                'name'     => 'club_count',
                'label'    => __('Club Count'),
                'title'    => __('Club Count'),
                'disabled' => $isElementDisabled                
            ]
            );
		
		$fieldset->addField(
    		'url_key',
    		'text',
    		[
                'name'     => 'url_key',
                'label'    => __('URL Key'),
                'title'    => __('URL Key'),
                'disabled' => $isElementDisabled
    		]
    		);	
			
        $fieldset->addField(
            'group_id',
            'select',
            [
                'label'    => __('Club Group'),
                'title'    => __('Club Group'),
                'name'     => 'group_id',
                'options'  => $this->_viewHelper->getGroupList(),
                'note'     => __('Club group must be selected'),
                'disabled' => $isElementDisabled
            ]
        );

    	$fieldset->addField(
    		'image',
    		'image',
    		[
                'name'     => 'image',
                'label'    => __('Image'),
                'title'    => __('Image'),
                'disabled' => $isElementDisabled
    		]
    		);


    	$fieldset->addField(
    		'clogo',
    		'image',
    		[
                'name'     => 'clogo',
                'label'    => __('Club Logo'),
                'title'    => __('Club Logo'),
                'disabled' => $isElementDisabled
    		]
    		);

        $fieldset->addField(
            'club_banner',
            'image',
            [
                'name'     => 'club_banner',
                'label'    => __('Club Banner'),
                'title'    => __('Club anner'),
                'disabled' => $isElementDisabled
            ]
            );
    
    
        $fieldset->addField(
            'club_watermark_image',
            'image',
            [
                'name'     => 'club_watermark_image',
                'label'    => __('Club Watermark Image'),
                'title'    => __('club Watermark Image'),
                'disabled' => $isElementDisabled
            ]
            );
            
    	$fieldset->addField(
            'description',
            'editor',
            [
                'name'     => 'description',
                'style'    => 'height:200px;',
                'label'    => __('Description'),
                'title'    => __('Description'),
                'disabled' => $isElementDisabled,
                'config'   => $wysiwygConfig
            ]
        );

    	/**
         * Check is single store mode
         */
        if (!$this->_storeManager->isSingleStoreMode()) {
            $field = $fieldset->addField(
                'store_id',
                'multiselect',
                [
                    'name' => 'stores[]',
                    'label' => __('Store View'),
                    'title' => __('Store View'),
                    'values' => $this->_systemStore->getStoreValuesForForm(false, true),
                    'disabled' => $isElementDisabled
                ]
            );
            $renderer = $this->getLayout()->createBlock(
                'Magento\Backend\Block\Store\Switcher\Form\Renderer\Fieldset\Element'
            );
            $field->setRenderer($renderer);
        } else {
            $fieldset->addField(
                'store_id',
                'hidden',
                ['name' => 'stores[]', 'value' => $this->_storeManager->getStore(true)->getId()]
            );
            $model->setStoreId($this->_storeManager->getStore(true)->getId());
        }


        $fieldset->addField(
    		'position',
    		'text',
    		[
	    		'name' => 'position',
	    		'label' => __('Position'),
	    		'title' => __('Position'),
	    		'disabled' => $isElementDisabled
    		]
    		);

        $fieldset->addField(
            'status',
            'select',
            [
                'label' => __('Status'),
                'title' => __('Page Status'),
                'name' => 'status',
                'options' => $model->getAvailableStatuses(),
                'disabled' => $isElementDisabled
            ]
        );


    	$form->setValues($model->getData());
    	$this->setForm($form);

    	return parent::_prepareForm();
    }

    /**
     * Prepare label for tab
     *
     * @return \Magento\Framework\Phrase
     */
    public function getTabLabel()
    {
        return __('Club Information');
    }

    /**
     * Prepare title for tab
     *
     * @return \Magento\Framework\Phrase
     */
    public function getTabTitle()
    {
        return __('Club Information');
    }

    /**
     * {@inheritdoc}
     */
    public function canShowTab()
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function isHidden()
    {
        return false;
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->_authorization->isAllowed($resourceId);
    }
}