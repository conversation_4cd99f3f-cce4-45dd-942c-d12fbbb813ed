<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Club\Block\Adminhtml\System\Widget\Form\Renderer;

use Magento\Framework\Data\Form\Element\AbstractElement;

/**
 * Form element default renderer
 *
 * <AUTHOR> Core Team <<EMAIL>>
 */
class Separator extends \Magento\Backend\Block\Template implements \Magento\Framework\Data\Form\Element\Renderer\RendererInterface
{
    public function render(AbstractElement $element)
    {
        $this->_element = $element;

        return $this->toHtml();
    }
}
