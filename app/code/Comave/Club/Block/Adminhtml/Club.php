<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Adminhtml;

/**
 * Adminhtml cms pages content block
 */
class Club extends \Magento\Backend\Block\Widget\Grid\Container
{
    /**
     * Block constructor
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_controller = 'adminhtml_club';
        $this->_blockGroup = 'Comave_Club';
        $this->_headerText = __('Manage Clubs');

        parent::_construct();

        if ($this->_isAllowedAction('Comave_Club::club_save')) {
            $this->buttonList->update('add', 'label', __('Add New Club'));
        } else {
            $this->buttonList->remove('add');
        }
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->_authorization->isAllowed($resourceId);
    }
}
