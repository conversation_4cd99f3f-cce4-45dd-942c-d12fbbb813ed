<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="club_listing_data_source" xsi:type="string">Comave\Club\Model\ResourceModel\Club\Grid\Collection</item>
                <item name="group_listing_data_source" xsi:type="string">Comave\Club\Model\ResourceModel\Group\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
	<type name="Comave\Club\Model\ResourceModel\Club\Grid\Collection">
		<arguments>
			<argument name="mainTable" xsi:type="string">comave_club</argument>
			<argument name="eventPrefix" xsi:type="string">comave_club_grid_collection</argument>
			<argument name="eventObject" xsi:type="string">club_grid_collection</argument>
			<argument name="resourceModel" xsi:type="string">Comave\Club\Model\ResourceModel\Club</argument>
		</arguments>
	</type>
	<type name="Comave\Club\Model\ResourceModel\Group\Grid\Collection">
		<arguments>
			<argument name="mainTable" xsi:type="string">comave_club_group</argument>
			<argument name="eventPrefix" xsi:type="string">comave_club_grid_collection</argument>
			<argument name="eventObject" xsi:type="string">group_grid_collection</argument>
			<argument name="resourceModel" xsi:type="string">Comave\Club\Model\ResourceModel\Club</argument>
		</arguments>
	</type>
	<type name="Comave\Club\Model\Layer\Resolver">
		<arguments>
			<argument name="layersPool" xsi:type="array">
				<item name="club" xsi:type="string">Comave\Club\Model\Layer\Club</item>
			</argument>
		</arguments>
	</type>
	<type name="Comave\Club\Model\Layer\Club">
		<arguments>
			<argument name="context" xsi:type="object">Magento\Catalog\Model\Layer\Category\Context</argument>
		</arguments>
	</type>
	<type name="Comave\Club\Block\Club\Product\ListProduct">
		<arguments>
			<argument name="layerResolver" xsi:type="object">Comave\Club\Model\Layer\Resolver</argument>
		</arguments>
	</type>
	<virtualType name="ClubGirdFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>
	<virtualType name="ClubGridDataProvider" type="Comave\Club\Ui\DataProvider\Club\ClubDataProvider">
        <arguments>
            <argument name="filterPool" xsi:type="object" shared="false">ClubGirdFilterPool</argument>
        </arguments>
    </virtualType>
    <virtualType name="GroupGridDataProvider" type="Comave\Club\Ui\DataProvider\Club\GroupDataProvider">
        <arguments>
            <argument name="filterPool" xsi:type="object" shared="false">ClubGirdFilterPool</argument>
        </arguments>
    </virtualType>
    <preference for="Comave\Club\Api\ClubRepositoryInterface" type="Comave\Club\Model\Repository\ClubRepository" />
</config>
