<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Comave_Club::main_menu" title="Comave Club">
                    <resource id="Comave_Club::club" title="Clubs" sortOrder="10">
                        <resource id="Comave_Club::club_edit" title="Edit Club" sortOrder="10" />
                        <resource id="Comave_Club::club_save" title="Save Club" sortOrder="20" />
                        <resource id="Comave_Club::club_delete" title="Delete Club" sortOrder="30" />
                    </resource>
                    <resource id="Comave_Club::group" title="Groups" sortOrder="30">
                        <resource id="Comave_Club::group_edit" title="Edit Group" sortOrder="10" />
                        <resource id="Comave_Club::group_save" title="Save Group" sortOrder="20" />
                        <resource id="Comave_Club::group_delete" title="Delete Group" sortOrder="30" />
                    </resource>
                    <resource id="Comave_Club::import" title="Import Clubs Products" sortOrder="40">
                        <resource id="Comave_Club::import_products" title="Import" sortOrder="10" />
                        <resource id="Comave_Club::import_save" title="Save" sortOrder="20" />
                    </resource>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Comave_Club::config_club" title="Club" />
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>