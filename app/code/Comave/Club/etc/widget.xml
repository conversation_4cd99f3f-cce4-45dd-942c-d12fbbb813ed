<?xml version="1.0" encoding="UTF-8"?>
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
	<widget id="comave_club_list" class="Comave\Club\Block\Widget\Clublist">
		<label translate="true">ComAve Club List</label>
		<description translate="true">Get club list</description>
		<parameters>
			<parameter name="widget_title" xsi:type="text" visible="true" sort_order="0">
				<label translate="true">Title</label>
			</parameter>
			<parameter name="cmsblock" xsi:type="select" visible="true" sort_order="10" source_model="Comave\Club\Model\Config\Source\Staticblock">
				<label translate="true">Select Static Block</label>
			</parameter>
			<parameter name="pretext_html" xsi:type="conditions" visible="true" sort_order="15" class="Comave\Club\Block\Adminhtml\Widget\Form\Field\WysiwygEditor">
				<label translate="true">Pretext HTML</label>
			</parameter>
			<parameter name="club_groups" xsi:type="multiselect"  visible="true" sort_order="20" source_model="Comave\Club\Model\Source\Clubgrouplist">
				<label translate="true">Club Group</label>
			</parameter>
			<parameter name="number_item" required="true" xsi:type="text"  visible="true" sort_order="30">
				<label translate="true">Number of Club to Display</label>
				<value>12</value>
			</parameter>
			<parameter name="number_item_per_column" required="true" xsi:type="text"  visible="true" sort_order="20">
				<label translate="true">Number Item per Column</label>
				<description><![CDATA[Default is 1]]></description>
				<value>1</value>
			</parameter>
			<parameter name="show_club_name" xsi:type="select"  visible="true" sort_order="40" source_model="Magento\Config\Model\Config\Source\Yesno">
				<label translate="true">Show Club Name</label>
			</parameter>
			<parameter name="addition_class" xsi:type="text"  visible="true" sort_order="50">
				<label translate="true">Addtion Class</label>
			</parameter>
			<parameter name="carousel_layout" xsi:type="select"  visible="true" sort_order="60" source_model="Comave\Club\Model\Source\Carousellayout">
				<label translate="true">Carousel Layout</label>
			</parameter>
			<parameter name="mobile_items" xsi:type="text"  visible="true" sort_order="70">
				<label translate="true">Number Columns On Page (Phones)</label>
				<description translate="true">Show number items when screen size bellow 480px</description>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
				<value>4</value>
			</parameter>
			<parameter name="tablet_small_items" xsi:type="text"  visible="true" sort_order="80">
				<label translate="true">Number Columns On Page (Phones to Small tablets)</label>
				<description translate="true">Show number items when screen size between 641px and 480px</description>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
				<value>3</value>
			</parameter>
			<parameter name="tablet_items" xsi:type="text"  visible="true" sort_order="90">
				<label translate="true">Number Columns On Page (Phones to tablets)</label>
				<description translate="true">Show number items when screen size between 768px and 641px</description>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
				<value>3</value>
			</parameter>
			<parameter name="portrait_items" xsi:type="text"  visible="true" sort_order="100">
				<label translate="true">Number Columns On Page (Portrait tablets)</label>
				<description translate="true">Show number items when screen size between 979px and 769px</description>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
				<value>3</value>
			</parameter>
			<parameter name="default_items" xsi:type="text"  visible="true" sort_order="110">
				<label translate="true">Number Columns On Page (Default)</label>
				<description translate="true">Show number items when screen size between 1199px and 980px</description>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
				<value>6</value>
			</parameter>
			<parameter name="large_items" xsi:type="text"  visible="true" sort_order="120">
				<label translate="true">Number Columns On Page (Large display)</label>
				<description translate="true">Show number items when screen size 1200px and up</description>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
				<value>3</value>
			</parameter>
			<parameter name="autoplay" xsi:type="select"  visible="true" sort_order="130" source_model="Magento\Config\Model\Config\Source\Yesno">
				<label translate="true">Autoplay</label>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
			</parameter>
			<parameter name="autoplay_timeout" xsi:type="text" visible="true" sort_order="140">
				<label translate="true">Autoplay Timeout</label>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
			</parameter>
			<parameter name="item_per_page" xsi:type="text" visible="true" sort_order="150">
				<label translate="true">Item Per Page</label>
				<depends><parameter name="carousel_layout" value="bootstrap_carousel" /></depends>
			</parameter>
			<parameter name="lg_column_item" xsi:type="text" visible="true" sort_order="160">
				<label translate="true">Number Column on Desktop</label>
				<description><![CDATA[Large devices Desktops (≥1200px)]]></description>
				<depends><parameter name="carousel_layout" value="bootstrap_carousel" /></depends>
			</parameter>
			<parameter name="md_column_item" xsi:type="text" visible="true" sort_order="170">
				<label translate="true">Number Column on Desktop</label>
				<description><![CDATA[Medium devices Desktops (≥992px)]]></description>
				<depends><parameter name="carousel_layout" value="bootstrap_carousel" /></depends>
			</parameter>
			<parameter name="sm_column_item" xsi:type="text" visible="true" sort_order="170">
				<label translate="true">Number Column on Tablets</label>
				<description><![CDATA[Small devices Tablets (≥768px)]]></description>
				<depends><parameter name="carousel_layout" value="bootstrap_carousel" /></depends>
			</parameter>
			<parameter name="xs_column_item" xsi:type="text" visible="true" sort_order="180">
				<label translate="true">Number Column on Phones</label>
				<description><![CDATA[Extra small devices Phones (<768px)]]></description>
				<depends><parameter name="carousel_layout" value="bootstrap_carousel" /></depends>
			</parameter>
			<parameter name="interval" xsi:type="text" visible="true" sort_order="190">
				<label translate="true">Interval</label>
				<description><![CDATA[The amount of time to delay between automatically cycling an item. If false, carousel will not automatically cycle.]]></description>
				<depends><parameter name="carousel_layout" value="bootstrap_carousel" /></depends>
			</parameter>
			<parameter name="rtl" xsi:type="select"  visible="true" sort_order="200" source_model="Magento\Config\Model\Config\Source\Yesno">
				<label translate="true">RTL</label>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
			</parameter>
			<parameter name="loop" xsi:type="select"  visible="true" sort_order="210" source_model="Magento\Config\Model\Config\Source\Yesno">
				<label translate="true">Loop</label>
				<depends><parameter name="carousel_layout" value="owl_carousel" /></depends>
			</parameter>
			<parameter name="dots" xsi:type="select"  visible="true" sort_order="220" source_model="Magento\Config\Model\Config\Source\Yesno">
				<label translate="true">Show dots navigations</label>
			</parameter>
			<parameter name="nav" xsi:type="select"  visible="true" sort_order="230" source_model="Magento\Config\Model\Config\Source\Yesno">
				<label translate="true">Show next/prev buttons.</label>
			</parameter>
			<parameter name="nav_prev" xsi:type="text" visible="true" sort_order="240">
				<label translate="true">Nav Prev Text</label>
				<depends><parameter name="nav" value="1" /></depends>
				<value>Prev</value>
			</parameter>
			<parameter name="nav_next" xsi:type="text" visible="true" sort_order="250">
				<label translate="true">Nav Next Text</label>
				<depends><parameter name="nav" value="1" /></depends>
				<value>Next</value>
			</parameter>
			<parameter name="widget_template" xsi:type="text" visible="true" sort_order="1000">
				<label translate="true">Template</label>
			</parameter>
		</parameters>
	</widget>
</widgets>
