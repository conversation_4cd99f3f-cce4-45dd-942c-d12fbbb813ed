<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../Config/etc/system_file.xsd">
	<system>
        <tab id="comave_club_tab" translate="label" sortOrder="1">
            <label>ComAve Clubs</label>
        </tab>
		<section id="comave_club" translate="label" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
			<class>separator-top</class>
			<label>Shop by Club Setting</label>
			<tab>comave_club_tab</tab>
			<resource>Comave_Club::config_club</resource>
            <group id="info" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <frontend_model>Comave\Club\Block\System\Config\Form\Field\Info</frontend_model>
            </group> 
			<group id="general_settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>General Settings</label>
				<field id="enable" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="route" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Club List Url</label>
				</field>
				<field id="url_prefix" translate="label comment" type="text" sortOrder="24" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Club Url Prefix</label>
				</field>
				<field id="url_suffix" translate="label comment" type="text" sortOrder="28" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Club Url Suffix</label>
				</field>
				<field id="enable_menu" translate="label comment" type="select" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enable Club Categrories Menu</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="enable_search" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enable Search Club Block</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
			<group id="product_view_page" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Club Info On Product View Page</label>
				<field id="enable_club_info" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled Club Block</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="club_layout_listing" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club As Listing</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_club_text" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Text</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_club_description" translate="label comment" type="select" sortOrder="32" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Description</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="club_text" translate="label comment" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Club Text</label>
				</field>
				<field id="show_club_image" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Logo</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_club_name" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Name</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
			<group id="club_list_page" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Club List Page</label>
				<field id="layout" translate="label comment" type="select" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Layout</label>
					<source_model>Comave\Club\Model\Config\Source\Clublistlayout</source_model>
				</field>
				<field id="show_club_name" translate="label comment" type="select" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Name</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="item_per_page" translate="label comment" type="text" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Club Per Page</label>
					<comment>Empty to show all item</comment>
				</field>
				<field id="seo_config_heading" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>SEO - Search Engine Optimization</label>
					<frontend_model>Comave\Club\Block\Adminhtml\System\Config\Form\Field\Heading</frontend_model>
				</field>
				<field id="page_title" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="20">
					<label>Page Title</label>
				</field>
				<field id="meta_description" type="textarea"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="30">
					<label>Meta Description</label>
				</field>
				<field id="meta_keywords" type="textarea"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="40">
					<label>Meta Keywords</label>
				</field>
				<field id="grid_config_heading" translate="label comment" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Grid Layout</label>
					<frontend_model>Comave\Club\Block\Adminhtml\System\Config\Form\Field\Heading</frontend_model>
				</field>
				<field id="lg_column_item" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Large Desktop</label>
					<comment><![CDATA[Large devices Desktops (≥1200px)]]></comment>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
				</field>
				<field id="md_column_item" translate="label comment" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Desktop</label>
					<comment><![CDATA[Medium devices Desktops (≥992px)]]></comment>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
				</field>
				<field id="sm_column_item" translate="label comment" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Tablets</label>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
					<comment><![CDATA[Small devices Tablets (≥768px)]]></comment>
				</field>
				<field id="xs_column_item" translate="label comment" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Phones</label>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
					<comment><![CDATA[Extra small devices Phones (<768px)]]></comment>
				</field>
			</group>
			<group id="group_page" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Group Page</label>
				<field id="show_club_name" translate="label comment" type="select" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Name</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="item_per_page" translate="label comment" type="text" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Club Per Page</label>
					<comment>Empty to show all item</comment>
				</field>
				<field id="lg_column_item" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Large Desktop</label>
					<comment><![CDATA[Large devices Desktops (≥1200px)]]></comment>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
				</field>
				<field id="md_column_item" translate="label comment" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Desktop</label>
					<comment><![CDATA[Medium devices Desktops (≥992px)]]></comment>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
				</field>
				<field id="sm_column_item" translate="label comment" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Tablets</label>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
					<comment><![CDATA[Small devices Tablets (≥768px)]]></comment>
				</field>
				<field id="xs_column_item" translate="label comment" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Number Column on Phones</label>
					<source_model>Comave\Club\Model\Config\Source\Gridcolumns</source_model>
					<comment><![CDATA[Extra small devices Phones (<768px)]]></comment>
				</field>
			</group>
			<group id="club_block" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Club Block</label>
				<field id="enable" translate="label comment" type="select" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enable</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="title" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="10">
					<label>Title</label>
				</field>
				<field id="pretext" translate="label comment" type="editor" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Pretext HTML</label>
					<frontend_model>Comave\Club\Block\Adminhtml\System\Config\Form\Field\Editor</frontend_model>
					<comment><![CDATA[Show after title. Empty to hide]]></comment>
				</field>
				<field id="club_groups" type="multiselect" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="20">
					<label>Club Group</label>
					<source_model>Comave\Club\Model\Source\Clubgrouplist</source_model>
				</field>
				<field id="show_club_name" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show Club Name</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="number_item" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="40">
					<label>Number of Club to Display</label>
				</field>
				<field id="addition_class" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="50">
					<label>Addtion Class</label>
				</field>
				<field id="carousel_layout" type="select" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="60" >
					<label>Carousel Layout</label>
					<source_model>Comave\Club\Model\Source\Carousellayout</source_model>
				</field>
				<field id="number_item_per_column" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="65">
					<label>Number Item per Column</label>
					<comment >Default is 1</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="mobile_items" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="80">
					<label>Number Columns On Page (Phones)</label>
					<comment >Show number items when screen size bellow 480px</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="tablet_small_items" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="90">
					<label>Number Columns On Page (Phones to Small tablets)</label>
					<comment >Show number items when screen size between 641px and 480px</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="tablet_items" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="100">
					<label>Number Columns On Page (Phones to tablets)</label>
					<comment >Show number items when screen size between 768px and 641px</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="portrait_items" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="110">
					<label>Number Columns On Page (Portrait tablets)</label>
					<comment >Show number items when screen size between 979px and 769px</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="default_items" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="112">
					<label>Number Columns On Page (Default)</label>
					<comment >Show number items when screen size between 1199px and 980px</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="large_items" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="120">
					<label>Number Columns On Page (Large display)</label>
					<comment >Show number items when screen size 1200px and up</comment>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="autoplay" type="select"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="122">
					<label>Autoplay</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="autoplay_timeout" type="text"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="124">
					<label>Autoplay Timeout</label>
					<comment>Autoplay interval timeout.</comment>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="autoplay_pauseonhover" type="select"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="126">
					<label>Pause on Hover</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="item_per_page" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="130">
					<label>Item Per Page</label>
					<depends>
						<field id="carousel_layout">bootstrap_carousel</field>
					</depends>
				</field>
				<field id="lg_column_item" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="140">
					<label>Number Column on Desktop</label>
					<comment><![CDATA[Large devices Desktops (≥1200px)]]></comment>
					<depends>
						<field id="carousel_layout">bootstrap_carousel</field>
					</depends>
				</field>
				<field id="md_column_item" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="150">
					<label>Number Column on Desktop</label>
					<comment><![CDATA[Medium devices Desktops (≥992px)]]></comment>
					<depends>
						<field id="carousel_layout">bootstrap_carousel</field>
					</depends>
				</field>
				<field id="sm_column_item" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="160">
					<label>Number Column on Tablets</label>
					<comment><![CDATA[Small devices Tablets (≥768px)]]></comment>
					<depends>
						<field id="carousel_layout">bootstrap_carousel</field>
					</depends>
				</field>
				<field id="xs_column_item" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="170">
					<label>Number Column on Phones</label>
					<comment><![CDATA[Extra small devices Phones (<768px)]]></comment>
					<depends>
						<field id="carousel_layout">bootstrap_carousel</field>
					</depends>
				</field>
				<field id="interval" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="180">
					<label>Interval</label>
					<comment><![CDATA[The amount of time to delay between automatically cycling an item. If false, carousel will not automatically cycle.]]></comment>
					<depends>
						<field id="carousel_layout">bootstrap_carousel</field>
					</depends>
				</field>
				<field id="loop" type="select"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="185">
					<label>Loop</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="rtl" type="select"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="187">
					<label>RTL</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<depends>
						<field id="carousel_layout">owl_carousel</field>
					</depends>
				</field>
				<field id="dots" type="select"  translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="190">
					<label>Show dots navigations</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="nav" type="select" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="200">
					<label>Show next/prev buttons.</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="nav_prev" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="210">
					<label>Nav Prev</label>
				</field>
				<field id="nav_next" type="text" translate="label comment" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="220">
					<label>Nav Next</label>
				</field>
			</group>
			<group id="dynamic_banner" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Dynamic Banner</label>
					<field id="enable" translate="label comment" type="select" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enable</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
		<section id="weltpixel_owl_carousel_config" translate="label" type="text" sortOrder="180" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>OWL Product Carousels Pro</label>
            <tab>weltpixel</tab>
            <resource>WeltPixel_OwlCarouselSlider::owlcarouselslider_settings</resource>
             <group id="club_products" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Club Products Carousel Settings</label>

                <field id="status" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Club Product Carousel</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>In order for carousel to be displayed, make sure you have products defined as "New" and the carousel is inserted in your page/block. Insert the
                        carousel by following the documentation.</comment>
                    <tooltip>Club Products Carousel - will be populated with products defined as 'New' in your magento catalog.</tooltip>
                </field>
                <field id="title" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Title</label>
                    <comment>Insert custom title to be displayed on the carousel. For no title leave field empty.</comment>
                </field>
                <field id="show_price" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Price</label>
                    <comment>Show/Hide the product price for products in the carousel</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_addto" translate="label comment" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Add To Cart</label>
                    <comment>Show/Hide ‘Add to cart’ button for products in the carousel.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_wishlist" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Wishlist</label>
                    <comment>Show/Hide wishlist icon for products in the carousel.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_compare" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Compare</label>
                    <comment>Show/Hide Compare icon for products in the carousel</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_reviews_ratings" translate="label comment" type="select" sortOrder="65" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Reviews and Ratings</label>
                    <comment>Show/Hide Reviews and Ratings for products in the carousel</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="random_sort" translate="label comment" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sort Order</label>
                    <comment>Sort Order of the products in the carousel.</comment>
                    <source_model>Comave\Club\Model\Config\Source\SortOrder</source_model>
                </field>
                <field id="max_items" translate="label" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Max Items</label>
                    <comment>The total number of products to be loaded in the carousel.</comment>
                </field>
                <field id="slide_by" translate="label" type="text" sortOrder="83" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Slide By</label>
                    <comment><![CDATA[ Insert how many items to slide at once. Default value: 1 <br> Note: On breakpoints the value will auto adjust to visible items. ]]></comment>
                    <validate>validate-number validate-greater-than-zero validate-no-empty</validate>
                </field>
                <!--Separator Slider-->

                <field id="separatorSlider" translate="label" type="text" sortOrder="85" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Comave\Club\Block\Adminhtml\System\Config\Separatorslide</frontend_model>
                </field>
                <field id="loop" translate="label" type="select" sortOrder="140" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Loop</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>Inifnity loop. Duplicate last and first items to get loop illusion.</comment>
                </field>
                <field id="margin" translate="label" type="text" sortOrder="150" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Margin</label>
                    <comment>Set right margin for each item in carousel. Example: for a margin of 30px enter 30 in the field.</comment>
                </field>
                <field id="lazyLoad" translate="label" type="select" sortOrder="160" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>LazyLoad</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>Lazy Load delays loading of images. Images outside of viewport are not loaded until user scrolls to them.</comment>
                </field>
                <field id="autoplay" translate="label" type="select" sortOrder="170" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Autoplay</label>
                    <comment>Autoplay the carousel.</comment>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                </field>
                <field id="autoplayTimeout" translate="label" type="text" sortOrder="180" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>AutoplayTimeout</label>
                    <depends>
                        <field id="autoplay">1</field>
                    </depends>
                    <comment>Autoplay interval timeout. Set time between changing products (in milliseconds, for Example: 4000, for 4 seconds))</comment>
                </field>
                <field id="autoplayHoverPause" translate="label" type="select" sortOrder="190" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>AutoplayHoverPause</label>
                    <comment>Set the autoplay to pause on mouse hover.</comment>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <depends>
                        <field id="autoplay">1</field>
                    </depends>
                </field>
                <field id="navSpeed" translate="label comment" type="text" sortOrder="191" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Next/Prev button Transition Speed</label>
                    <comment>Set the speed for the transition animation when clicking the Next/Prev buttons. Insert value in ms. Write “4000” for a 4 second timeout.</comment>
                </field>
                <field id="dotsSpeed" translate="label comment" type="text" sortOrder="192" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Dots Transition Speed</label>
                    <comment>Set the speed for the transition animation when clicking the Dots. Insert value in ms. Write “4000” for a 4 second timeout.</comment>
                </field>
                <field id="rtl" translate="label comment" type="select" sortOrder="193" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Direction Right To Left</label>
                    <comment>If True, each banner will slide from right to left.</comment>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                </field>
                <field id="nav_design" translate="label comment" type="select" sortOrder="194" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Prev/Next buttons design</label>
                    <source_model>Comave\Club\Model\Config\Source\PrevNextDesign</source_model>
                </field>
                <field id="nav_prev_label" translate="label comment" type="text" sortOrder="195" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Prev Label</label>
                    <comment>Insert custom label for  previous button. If empty, only arrows are displayed.</comment>
                    <depends>
                        <field id="nav_design">2</field>
                    </depends>
                </field>
                <field id="nav_next_label" translate="label comment" type="text" sortOrder="195" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Next Label</label>
                    <comment>Insert custom label for next button. If empty, only arrows are displayed.</comment>
                    <depends>
                        <field id="nav_design">2</field>
                    </depends>
                </field>

                <!--Separator Breakpoints-->

                <field id="Separatorbreakpoint" translate="label" type="text" sortOrder="195" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Comave\Club\Block\Adminhtml\System\Config\Separatorbreakpoint</frontend_model>
                </field>

                <!--Separator Breakpoint 1-->
                <field id="s_1" translate="label" type="text" sortOrder="197" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Comave\Club\Block\Adminhtml\System\Config\Separatorbreakpoint</frontend_model>
                </field>

                <field id="nav_brk1" translate="label" type="select" sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Next/Prev Buttons</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, next/prev buttons are displayed.</comment>
                </field>
                <field id="dots_brk1" translate="label" type="select" sortOrder="205" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Dots</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, dots are displayed under carousel.</comment>
                </field>
                <field id="dotsEach_brk1" translate="label comment" type="select" sortOrder="206" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Dots for each Item</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, Show dots for each item.</comment>
                    <depends>
                        <field id="dots_brk1">1</field>
                    </depends>
                </field>
                <field id="items_brk1" translate="label" type="text" sortOrder="210" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Items</label>
                    <comment>The number of items you want to see on the screen. This value should be smaller than Max Items. </comment>
                </field>
                <field id="center_brk1" translate="label" type="select" sortOrder="220" showInDefault="1" showInWebsite="1" showInStore="1">
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <label>Center</label>
                    <comment>If True, carousel is centered on the screen. Works well even with an odd number of items.</comment>
                </field>
                <field id="stagePadding_brk1" translate="label" type="text" sortOrder="230" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>StagePadding</label>
                    <depends>
                        <field id="center_brk1">0</field>
                    </depends>
                    <comment>Padding left and right on stage (can see neighbours).</comment>
                </field>

                <!--Separatorbreakpoint 2-->
                <field id="s_2" translate="label" type="text" sortOrder="235" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Comave\Club\Block\Adminhtml\System\Config\Separatorbreakpoint</frontend_model>
                </field>

                <field id="nav_brk2" translate="label" type="select" sortOrder="240" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Next/Prev Buttons</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, next/prev buttons are displayed.</comment>
                </field>
                <field id="dots_brk2" translate="label" type="select" sortOrder="245" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Dots</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, dots are displayed under carousel.</comment>
                </field>
                <field id="dotsEach_brk2" translate="label comment" type="select" sortOrder="246" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Dots for each Item</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, Show dots for each item.</comment>
                    <depends>
                        <field id="dots_brk2">1</field>
                    </depends>
                </field>
                <field id="items_brk2" translate="label" type="text" sortOrder="250" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Items</label>
                    <comment>The number of items you want to see on the screen. This value should be smaller than Max Items. </comment>
                </field>
                <field id="center_brk2" translate="label" type="select" sortOrder="260" showInDefault="1" showInWebsite="1" showInStore="1">
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <label>Center</label>
                    <comment>If True, carousel is centered on the screen. Works well even with an odd number of items.</comment>
                </field>
                <field id="stagePadding_brk2" translate="label" type="text" sortOrder="270" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>StagePadding</label>
                    <depends>
                        <field id="center_brk2">0</field>
                    </depends>
                    <comment>Padding left and right on stage (can see neighbours).</comment>
                </field>

                <!--Separatorbreakpoint 3-->
                <field id="s_3" translate="label" type="text" sortOrder="275" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Comave\Club\Block\Adminhtml\System\Config\Separatorbreakpoint</frontend_model>
                </field>

                <field id="nav_brk3" translate="label" type="select" sortOrder="280" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Next/Prev Buttons</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, next/prev buttons are displayed.</comment>
                </field>
                <field id="dots_brk3" translate="label" type="select" sortOrder="285" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Dots</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, dots are displayed under carousel.</comment>
                </field>
                <field id="dotsEach_brk3" translate="label comment" type="select" sortOrder="286" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Dots for each Item</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, Show dots for each item.</comment>
                    <depends>
                        <field id="dots_brk3">1</field>
                    </depends>
                </field>
                <field id="items_brk3" translate="label" type="text" sortOrder="290" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Items</label>
                    <comment>The number of items you want to see on the screen. This value should be smaller than Max Items. </comment>
                </field>
                <field id="center_brk3" translate="label" type="select" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <label>Center</label>
                    <comment>If True, carousel is centered on the screen. Works well even with an odd number of items.</comment>
                </field>
                <field id="stagePadding_brk3" translate="label" type="text" sortOrder="310" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>StagePadding</label>
                    <depends>
                        <field id="center_brk3">0</field>
                    </depends>
                    <comment>Padding left and right on stage (can see neighbours).</comment>
                </field>

                <!--Separatorbreakpoint 3-->
                <field id="s_4" translate="label" type="text" sortOrder="315" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Comave\Club\Block\Adminhtml\System\Config\Separatorbreakpoint</frontend_model>
                </field>

                <field id="nav_brk4" translate="label" type="select" sortOrder="320" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Next/Prev Buttons</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, next/prev buttons are displayed.</comment>
                </field>
                <field id="dots_brk4" translate="label" type="select" sortOrder="325" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Dots</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, dots are displayed under carousel.</comment>
                </field>
                <field id="dotsEach_brk4" translate="label comment" type="select" sortOrder="326" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Dots for each Item</label>
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <comment>If True, Show dots for each item.</comment>
                    <depends>
                        <field id="dots_brk4">1</field>
                    </depends>
                </field>
                <field id="items_brk4" translate="label" type="text" sortOrder="330" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Items</label>
                    <comment>The number of items you want to see on the screen. This value should be smaller than Max Items. </comment>
                </field>
                <field id="center_brk4" translate="label" type="select" sortOrder="340" showInDefault="1" showInWebsite="1" showInStore="1">
                    <source_model>Comave\Club\Model\Config\Source\Truefalse</source_model>
                    <label>Center</label>
                    <comment>If True, carousel is centered on the screen. Works well even with an odd number of items.</comment>
                </field>
                <field id="stagePadding_brk4" translate="label" type="text" sortOrder="350" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>StagePadding</label>
                    <depends>
                        <field id="center_brk4">0</field>
                    </depends>
                    <comment>Padding left and right on stage (can see neighbours).</comment>
                </field>
            </group>

        </section>
	</system>
</config>