<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="catalog_product_edit_action">
        <observer name="comave_club_loadproductclub" instance="Comave\Club\Observer\LoadProductClub" />
    </event>
    <event name="controller_action_catalog_product_save_entity_after">
        <observer name="comave_club_saveproductclub" instance="Comave\Club\Observer\SaveProductClub" />
    </event>
    <event name="catalog_product_save_after">
        <observer name="comave_club_saveproductclub" instance="Comave\Club\Observer\SaveProductClubModel" />
    </event>
    <event name="catalog_product_attribute_update_after">
    	<observer name="comave_club_saveattributeproductclub" instance="Comave\Club\Observer\MassUpdateAttributeClubModel" />
    </event>
</config>
