<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/layout_generic.xsd">
    <container name="root" label="Root">
        <block class="Comave\Club\Block\Adminhtml\Club\Edit\Tab\Products" name="club.edit.tab.products"/>
        <block class="Magento\Backend\Block\Widget\Grid\Serializer" name="related_grid_serializer">
            <arguments>
                <argument name="input_names" xsi:type="string">product_position</argument>
                <argument name="grid_block" xsi:type="string">club.edit.tab.products</argument>
                <argument name="callback" xsi:type="string">getSelectedClubProducts</argument>
                <argument name="input_element_name" xsi:type="string">links[related]</argument>
                <argument name="reload_param_name" xsi:type="string">products</argument>
            </arguments>
        </block>
    </container>
</layout>