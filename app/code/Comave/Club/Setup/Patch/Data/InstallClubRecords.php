<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Club\Setup\Patch\Data;

use Comave\Marketplace\Model\FixtureManager;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class InstallClubRecords implements DataPatchInterface
{
    /**
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly FixtureManager $fixtureManager
    ) {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();
        $clubGroup = $this->insertClubGroup();
        $this->insertClubs($clubGroup);
        $this->setClubStore();
        $this->moduleDataSetup->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return int
     */
    private function insertClubGroup(): int
    {
        $data = [
            'name' => 'Comave',
            'url_key' => 'comave',
            'position' => 0,
            'status' => 1,
            'shown_in_sidebar' => 0,
        ];
        $table = $this->moduleDataSetup->getConnection()->getTableName('comave_club_group');
        $this->moduleDataSetup->getConnection()->insert($table, $data);

        return (int)$this->moduleDataSetup->getConnection()->lastInsertId($table);
    }

    /**
     * @param int $clubGroup
     * @return void
     */
    private function insertClubs(int $clubGroup): void
    {
        $this->fixtureManager->setModuleName('Comave_Club');
        $clubs = $this->fixtureManager->getData('clubs');
        foreach ($clubs as $index => $club) {
            $clubs[$index]['group_id'] = $clubGroup;
        }
        $table = $this->moduleDataSetup->getConnection()->getTableName('comave_club');
        $this->moduleDataSetup->getConnection()->insertMultiple($table, $clubs);
    }

    /**
     * @return void
     */
    private function setClubStore(): void
    {
        $clubs = $this->getClubs();
        $table = $this->moduleDataSetup->getConnection()->getTableName('comave_club_store');
        $this->moduleDataSetup->getConnection()->insertMultiple($table, $clubs);
    }

    private function getClubs(): array
    {
        $clubs = [];
        $select = $this->moduleDataSetup->getConnection()->select();
        $select->from(
            $this->moduleDataSetup->getConnection()->getTableName('comave_club')
        );
        $data = $this->moduleDataSetup->getConnection()->fetchAll($select);
        foreach ($data as $row) {
            $club['club_id'] = $row['club_id'];
            $club['store_id'] = 0;
            $clubs[] = $club;
        }

        return $clubs;
    }
}
