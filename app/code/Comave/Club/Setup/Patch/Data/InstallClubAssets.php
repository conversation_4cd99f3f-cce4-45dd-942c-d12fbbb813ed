<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Club\Setup\Patch\Data;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Component\ComponentRegistrar;
use Magento\Framework\Component\ComponentRegistrarInterface;
use Magento\Framework\Filesystem;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use RecursiveDirectoryIteratorFactory;
use RecursiveIteratorIteratorFactory;

class InstallClubAssets implements DataPatchInterface
{
    /**
     * @string
     */
    private const string CLUB_ASSETS_PATH = '%s/install-data/club/assets';
    /**
     * @string
     */
    private const string CLUB_MEDIA_PATH = 'comave/club';

    /**
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     * @param \Magento\Framework\Filesystem $fileSystem
     * @param \Magento\Framework\Component\ComponentRegistrarInterface $componentRegistrar
     * @param \RecursiveDirectoryIteratorFactory $recursiveDirectoryIteratorFactory
     * @param \RecursiveIteratorIteratorFactory $recursiveIteratorIteratorFactory
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly Filesystem $fileSystem,
        private readonly ComponentRegistrarInterface $componentRegistrar,
        private readonly RecursiveDirectoryIteratorFactory $recursiveDirectoryIteratorFactory,
        private readonly RecursiveIteratorIteratorFactory $recursiveIteratorIteratorFactory
    ) {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();
        if ($this->setupClubAssetsDeployment()) {
            $this->deployClubAssets();
        }
        $this->moduleDataSetup->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return void
     */
    private function deployClubAssets(): void
    {
        $clubAssetsPath = sprintf(
            self::CLUB_ASSETS_PATH,
            $this->componentRegistrar->getPath(
                ComponentRegistrar::MODULE,
                'Comave_Club'
            )
        );
        $assets = $this->getDirectoryFiles($clubAssetsPath);
        foreach ($assets as $asset) {
            $this->createClubMedia($asset);
        }
    }

    /**
     * @param $dirPath
     * @return array
     */
    private function getDirectoryFiles($dirPath): array
    {
        $fileList = array();
        $iterator = $this->recursiveIteratorIteratorFactory->create([
            'iterator' => $this->recursiveDirectoryIteratorFactory->create([
                'directory' => $dirPath,
            ]),
        ]);
        foreach ($iterator as $resource) {
            if ($resource->isDir()) {
                continue;
            }

            $path = $resource->getPathname();
            $relativePath = str_replace("/", "", str_replace($dirPath, '', $path));

            $file = [];
            $file['absolute_path'] = $path;
            $file['relative_path'] = $relativePath;
            $fileList[] = $file;
        }

        return $fileList;
    }


    /**
     * @param array $assetFile
     * @return void
     */
    private function createClubMedia(array $assetFile): void
    {
        $clubMediaPath = $this->fileSystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath(
            self::CLUB_MEDIA_PATH
        );
        $targetPath = sprintf('%s/%s', $clubMediaPath, $assetFile['relative_path']);
        copy($assetFile['absolute_path'], $targetPath);
    }

    /**
     * @return bool
     */
    private function setupClubAssetsDeployment(): bool
    {
        $clubMediaPath = $this->fileSystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath(
            self::CLUB_MEDIA_PATH
        );
        if (file_exists($clubMediaPath)) {
            return true;
        }

        return mkdir($clubMediaPath, 0755, true);
    }
}
