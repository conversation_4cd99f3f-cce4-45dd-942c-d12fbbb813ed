<?php

declare(strict_types=1);

namespace Comave\Club\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Comave\Club\Setup\Patch\Data\UpdateProductClubAttribute;

class SetProductClubAttributeFilterable implements DataPatchInterface
{
    private const PRODUCT_CLUB_ATTRIBUTE_CODE = 'product_club';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->updateAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            self::PRODUCT_CLUB_ATTRIBUTE_CODE,
            'is_filterable',
            \Magento\Eav\Model\Entity\Attribute\Source\Boolean::VALUE_YES
        );

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    public static function getDependencies(): array
    {
        return [
            UpdateProductClubAttribute::class,
        ];
    }

    public function getAliases(): array
    {
        return [];
    }
}
