<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Club\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class UpdateClubAssetsRelations implements DataPatchInterface
{
    /**
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     * @param \Comave\Marketplace\Model\FixtureManager $fixtureManager
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
    ) {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();
        $this->updateClubsRelativeMediaPaths();
        $this->moduleDataSetup->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return void
     */
    private function updateClubsRelativeMediaPaths(): void
    {
        $table = $this->moduleDataSetup->getConnection()->getTableName('comave_club');
        $this->moduleDataSetup->getConnection()->update(
            $table,
            [
                'image' => new \Zend_Db_Expr('REPLACE(image, "coditron", "comave")'),
                'clogo' => new \Zend_Db_Expr('REPLACE(clogo, "coditron", "comave")'),
                'club_banner' => new \Zend_Db_Expr('REPLACE(club_banner, "coditron", "comave")'),
                'club_watermark_image' => new \Zend_Db_Expr('REPLACE(club_watermark_image, "coditron", "comave")'),
            ]
        );
    }
}
