<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller\Adminhtml\Import;
use AllowDynamicProperties;
use Magento\Framework\Json\EncoderInterface;
use Magento\Framework\App\Filesystem\DirectoryList;

#[AllowDynamicProperties] class Save extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $_filesystem;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $_scopeConfig;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * @var \Coditron\Setup\Helper\Import
     */
    protected $_coditronImport;

    /**
     * @var \Magento\Framework\App\Config\ConfigResource\ConfigInterface
     */
    protected $_configResource;

    /**
    * CSV Processor
    *
    * @var \Magento\Framework\File\Csv
    */
    protected $csvProcessor;

    /**
    * productFactory
    *
    * @var \Magento\Catalog\Model\ProductFactory
    */
    protected $productFactory;
    /**
     * @param \Magento\Backend\App\Action\Context                          $context
     * @param \Magento\Framework\View\Result\PageFactory                   $resultPageFactory
     * @param \Magento\Framework\Filesystem                                $filesystem
     * @param \Magento\Store\Model\StoreManagerInterface                   $storeManager
     * @param \Magento\Framework\App\Config\ScopeConfigInterface           $scopeConfig
     * @param \Magento\Framework\App\ResourceConnection                    $resource
     * @param \Magento\Framework\App\Config\ConfigResource\ConfigInterface $configResource
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\App\ResourceConnection $resource,
        \Magento\Framework\App\Config\ConfigResource\ConfigInterface $configResource,
        \Magento\Catalog\Model\Product\Media\Config $mediaConfig,
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Catalog\Model\ProductFactory $productFactory
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->_filesystem       = $filesystem;
        $this->_storeManager     = $storeManager;
        $this->_scopeConfig      = $scopeConfig;
        $this->_configResource   = $configResource;
        $this->_resource         = $resource;
        $this->mediaConfig       = $mediaConfig;
        $this->csvProcessor = $csvProcessor;
        $this->productFactory = $productFactory;
    }

    /**
     * Forward to edit
     *
     * @return \Magento\Backend\Model\View\Result\Forward
     */
    public function execute()
    {
        $data = $this->getRequest()->getParams();
        $filePath = $fileContent = '';
        try {
            $uploader = $this->_objectManager->create(
                'Magento\MediaStorage\Model\File\Uploader',
                ['fileId' => 'data_import_file']
            );

            $fileContent = '';
            if($uploader) {
                $tmpDirectory = $this->_objectManager->get('Magento\Framework\Filesystem')->getDirectoryRead(DirectoryList::TMP);
                $savePath     = $tmpDirectory->getAbsolutePath('comave/import');
                $uploader->setAllowRenameFiles(true);
                $result       = $uploader->save($savePath);
                $filePath = $tmpDirectory->getAbsolutePath('comave/import/' . $result['file']);
                $fileContent  = file_get_contents($tmpDirectory->getAbsolutePath('comave/import/' . $result['file']));
            }
        } catch (\Exception $e) {
            $this->messageManager->addError(__("Can't import data<br/> %1", $e->getMessage()));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('*/*/');
        }
        $delimiter = $this->getRequest()->getParam('split_symbol');
        if($delimiter) {
            $importData = $this->csvProcessor->setDelimiter($delimiter)->getData($filePath);
        } else {
            $importData = $this->csvProcessor->getData($filePath);
        }

        $store = $this->_storeManager->getStore($data['store_id']);
        $connection = $this->_resource->getConnection();
        if(!empty($importData)) {
            try{

                $heading = $importData[0];
                unset($importData[0]);
                $attribute_index = array_search("additional_attributes", $heading);
                $club_index = array_search("product_club", $heading);
                $sku_index = array_search("sku", $heading);
                if($sku_index !== false && ($club_index !== false || $attribute_index !== false)) {
                    $imported_counter = 0;
                   foreach($importData as $item_data) {
                        $product_sku = $item_data[$sku_index];
                        $product_sku = trim($product_sku);

                        if($product_sku) {
                            //get product id by sku
                            $product = $this->productFactory->create();
                            $product_id = $product->getIdBySku($product_sku);
                            $club_value = "";
                            $club_values = [];
                           if($club_index !== false) {
                                $club_value = $item_data[$club_index];
                                $club_value = trim($club_value);
                            } elseif($attribute_index !== false) {
                                $attr_value = $item_data[$attribute_index];
                                $tmp_arr = explode(",", $attr_value);
                                if($tmp_arr) {
                                    foreach($tmp_arr as $arr_item) {
                                        $tmp_attr = explode("=", $arr_item);
                                        if($tmp_attr && $tmp_attr[0] == "product_club") {
                                            $club_value = isset($tmp_attr[1])?$tmp_attr[1]:"";
                                            $club_value = replace('"','',$club_value);
                                            $club_value = trim($club_value);
                                            $club_values = explode(",",$club_value);
                                        }
                                    }
                                }
                            }
                            if($club_values && $product_id) {
                                //delete all old clubs via product_id
                                $club_model->deleteClubsByProduct($product_id);
                                foreach($club_values as $club_value){
                                    $club_value = trim($club_value);
                                    if($club_value){
                                        //get club id by club name
                                        $club_model = $this->_objectManager->create('Comave\Club\Model\Club');
                                        $club_model = $club_model->loadByClubName($club_value);

                                        //insert products to club
                                        if($club_model->getId()) {
                                            $club_model->saveProduct($product_id);
                                            $imported_counter++;
                                        }
                                    }
                                }

                            }
                        }

                    }

                    if($imported_counter)
                        $this->messageManager->addSuccess(__("Import successfully"));
                    else
                        $this->messageManager->addError(__("Can not found product or club item to imported."));
                } else {
                    $this->messageManager->addError(__("Required there columns: sku, product_club or additional_attributes"));
                }


            }catch(\Exception $e){
                $this->messageManager->addError(__("Can't import data<br/> %1", $e->getMessage()));
            }
        }
        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('*/*/');
    }
    /**
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Comave_Club::import_save');
    }
}
