<?php
declare(strict_types=1);

namespace Comave\Club\Controller\Adminhtml\Club;

use Comave\Club\Model\Club as ClubModel;
use Comave\Club\Model\Group;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Comave\Club\Model\ClubFactory;
use Comave\LixApiConnector\Helper\Data;
use Magento\Framework\Controller\ResultInterface;

class SyncClub extends Action
{
    public function __construct(
        Context $context,
        private readonly Data $dataHelper,
        private readonly ClubModel $clubManager,
        private readonly Group $groupManager,
    ) {
        parent::__construct($context);
    }

    protected function _isAllowed(): bool
    {
        return $this->_authorization->isAllowed('Comave_Club::club_save');
    }

    public function execute(): ResultInterface
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/syncclub.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $synced = 0;
        $orgId = $this->dataHelper->getOrganisationId();
        $clubresponce = $this->dataHelper->getClubList($orgId);

        $logger->info(print_r($clubresponce, true));

        $groupModel = $this->groupManager->getCollection();
        $collectionSize = $groupModel->getSize();

        $groupData = $groupModel->getData();

        foreach ($groupData as $group) {
            if ($group['name'] == "Comave") {
                $gid = $group['group_id'];
            } else {
                $gid = '';
            }
        }


        if ($clubresponce) {
            foreach ($clubresponce['data'] as $club) {
                $data = array();
                $data['name'] = $club['clubname'];
                $data['uniqueid'] = $club['clubuid'];
                $data['orgid'] = $orgId;
                $data['group_id'] = $gid;
                $data['status'] = ClubModel::STATUS_ENABLED;

                $clubModel = $this->clubManager->getCollection()
                    ->addFieldToFilter('uniqueid', $club['clubuid']);
                $collectionSize = $clubModel->getSize();

                if ($collectionSize == 1) {
                    $newClub = $clubModel->getData();
                    foreach ($newClub as $clubData) {
                        $clubIdLoad = $this->clubManager->load($clubData['club_id']);

                        $dataClub = array();
                        $dataClub['club_id'] = $clubData['club_id'];
                        $dataClub['name'] = $clubData['name'];
                        $logger->info(print_r($dataClub, true));

                        if (!empty($dataClub)) {
                            $clubIdLoad->setData($dataClub);
                            $this->clubResource->save($clubIdLoad);
                            $synced++;
                        }
                    }
                } else {
                    $logger->info(print_r($data, true));

                    $this->clubManager->setData($data);
                    $this->clubManager->save();
                    $synced++;
                }
            }
        }
        if ($synced) {
            $this->messageManager->addSuccessMessage(__('Club(s) %1 updated Successfully', $synced));
        }

        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('*/*/');
    }
}
