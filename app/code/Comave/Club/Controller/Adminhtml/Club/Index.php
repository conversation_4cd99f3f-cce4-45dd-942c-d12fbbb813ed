<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller\Adminhtml\Club;

class Index extends \Comave\Club\Controller\Adminhtml\Club
{
	/**
     * Check the permission to run it
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Comave_Club::club');
    }

	/**
	 * Club list action
	 *
	 * @return \Magento\Backend\Model\View\Result\Page|\Magento\Backend\Model\View\Result\Forward
	 */
	public function execute()
	{

		$resultPage = $this->resultPageFactory->create();

		/**
		 * Set active menu item
		 */
		$resultPage->setActiveMenu("Comave_Club::club");
		$resultPage->getConfig()->getTitle()->prepend(__('Clubs'));

		/**
		 * Add breadcrumb item
		 */
		$resultPage->addBreadcrumb(__('Clubs'),__('Clubs'));
		$resultPage->addBreadcrumb(__('Manage Clubs'),__('Manage Clubs'));

		return $resultPage;
	}
	
}