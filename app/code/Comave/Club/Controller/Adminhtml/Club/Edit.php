<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller\Adminhtml\Club;

use Magento\Backend\App\Action;

class Edit extends \Magento\Backend\App\Action
{
	/**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    /**
     * @param Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\Framework\Registry $registry
     */
    public function __construct(
        Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Framework\Registry $registry
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->_coreRegistry = $registry;
        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Comave_Club::club_edit');
    }

    /**
     * Init actions
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    protected function _initAction()
    {
        // load layout, set active menu and breadcrumbs
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Comave_Club::club')
            ->addBreadcrumb(__('Club'), __('Club'))
            ->addBreadcrumb(__('Manage Clubs'), __('Manage Clubs'));
        return $resultPage;
    }

    /**
     * Edit Club Page
     */
    public function execute()
    {
    	// 1. Get ID and create model
    	$id = $this->getRequest()->getParam('club_id');
    	$model = $this->_objectManager->create('Comave\Club\Model\Club');

    	// 2. Initial checking
    	if($id){
    		$model->load($id);
    		if(!$model->getId()){
    			$this->messageManager->addError(__('This club no longer exits. '));
    			/** \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
    			$resultRedirect = $this->resultRedirectFactory->create();

                return $resultRedirect->setPath('*/*/');
    		}
    	}

    	// 3. Set entered data if was error when we do save
    	$data = $this->_objectManager->get('Magento\Backend\Model\Session')->getFormData(true);
        if (!empty($data)) {
            $model->setData($data);
        }

        // 4. Register model to use later in blocks
        $this->_coreRegistry->register('comave_club', $model);

        // 5. Build edit form
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->_initAction();
        $resultPage->addBreadcrumb(
            $id ? __('Edit Club') : __('New Club'),
            $id ? __('Edit Club') : __('New Club')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Clubs'));
        $resultPage->getConfig()->getTitle()
            ->prepend($model->getId() ? $model->getname() : __('New Club'));

        return $resultPage;
    }
}