<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller\Adminhtml\Club;

use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Cms\Api\Data\PageInterface;
use Magento\Cms\Api\PageRepositoryInterface as PageRepository;

use Comave\Club\Model\Club as ClubModel;

class InlineEdit extends \Magento\Backend\App\Action
{

    /** @var PageRepository  */
    protected $clubRepository;

    /** @var JsonFactory  */
    protected $jsonFactory;

    /** @var clubModel */
    protected $clubModel;

    /**
     * @param Context $context
     * @param PageRepository $clubRepository
     * @param JsonFactory $jsonFactory
     * @param Comave\Club\Model\Club $clubModel
     */
    public function __construct(
        Context $context,
        PageRepository $clubRepository,
        JsonFactory $jsonFactory,
        ClubModel $clubModel
        ) {
        parent::__construct($context);
        $this->pageRepository = $clubRepository;
        $this->jsonFactory = $jsonFactory;
        $this->clubModel = $clubModel;
    }

    /**
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Json $resultJson */
        $resultJson = $this->jsonFactory->create();
        $error = false;
        $messages = [];

        $postItems = $this->getRequest()->getParam('items', []);
        if (!($this->getRequest()->getParam('isAjax') && count($postItems))) {
            return $resultJson->setData([
                'messages' => [__('Please correct the data sent.')],
                'error' => true,
                ]);
        }

        foreach (array_keys($postItems) as $clubId) {
            /** @var \Comave\Club\Model\Group $club */
            $club = $this->_objectManager->create('Comave\Club\Model\Club');
            $clubData = $postItems[$clubId];

            try {
                $club->load($clubId);
                $club->setData(array_merge($club->getData(), $clubData));
                $club->save();
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $messages[] = $this->getErrorWithgroupId($club, $e->getMessage());
                $error = true;
            } catch (\RuntimeException $e) {
                $messages[] = $this->getErrorWithgroupId($club, $e->getMessage());
                $error = true;
            } catch (\Exception $e) {
                $messages[] = $this->getErrorWithPageId(
                    $page,
                    __('Something went wrong while saving the page.')
                );
                $error = true;
            }
        }

        return $resultJson->setData([
            'messages' => 'abc',
            'error' => 'def'
            ]);
    }

    /**
     * Add page title to error message
     *
     * @param PageInterface $club
     * @param string $errorText
     * @return string
     */
    protected function getErrorWithgroupId($club, $errorText)
    {
        return '[Page ID: ' . $club->getId() . '] ' . $errorText;
    }
}