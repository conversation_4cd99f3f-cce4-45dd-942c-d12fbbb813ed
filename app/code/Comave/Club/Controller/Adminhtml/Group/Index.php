<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller\Adminhtml\Group;

class Index extends \Comave\Club\Controller\Adminhtml\Group
{
	/**
     * Check the permission to run it
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Comave_Club::group');
    }

	/**
	 * Club list action
	 *
	 * @return \Magento\Backend\Model\View\Result\Page|\Magento\Backend\Model\View\Result\Forward
	 */
	public function execute()
	{

		$resultPage = $this->resultPageFactory->create();

		/**
		 * Set active menu item
		 */
		$resultPage->setActiveMenu("Comave_Club::club");
		$resultPage->getConfig()->getTitle()->prepend(__('Club Groups'));

		/**
		 * Add breadcrumb item
		 */
		$resultPage->addBreadcrumb(__('Clubs'),__('Clubs'));
		$resultPage->addBreadcrumb(__('Manage Clubs'),__('Manage Club Groups'));

		return $resultPage;
	}
}