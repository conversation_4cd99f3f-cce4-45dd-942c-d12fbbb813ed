<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Controller;

use Magento\Framework\App\RouterInterface;
use Magento\Framework\App\ActionFactory;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\DataObject;
use Magento\Framework\Event\ManagerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Url;

class Router implements RouterInterface
{
    /**
     * @var \Magento\Framework\App\ActionFactory
     */
    protected $actionFactory;

    /**
     * Event manager
     * @var \Magento\Framework\Event\ManagerInterface
     */
    protected $eventManager;

    /**
     * Response
     * @var \Magento\Framework\App\ResponseInterface
     */
    protected $response;

    /**
     * @var bool
     */
    protected $dispatched;

    /**
     * Club Factory
     *
     * @var \Comave\Club\Model\Club $clubCollection
     */
    protected $_clubCollection;

    /**
     * Club Factory
     *
     * @var \Comave\Club\Model\Group $groupCollection
     */
    protected $_groupCollection;

    /**
     * Club Helper
     */
    protected $_clubHelper;

    /**
     * Store manager
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @param ActionFactory          $actionFactory   
     * @param ResponseInterface      $response        
     * @param ManagerInterface       $eventManager    
     * @param \Comave\Club\Model\Club $clubCollection 
     * @param \Comave\Club\Model\Group $groupCollection 
     * @param \Comave\Club\Helper\Data $clubHelper     
     * @param StoreManagerInterface  $storeManager    
     */
    public function __construct(
    	ActionFactory $actionFactory,
    	ResponseInterface $response,
        ManagerInterface $eventManager,
        \Comave\Club\Model\Club $clubCollection,
        \Comave\Club\Model\Group $groupCollection,
        \Comave\Club\Helper\Data $clubHelper,
        StoreManagerInterface $storeManager
        )
    {
    	$this->actionFactory = $actionFactory;
        $this->eventManager = $eventManager;
        $this->response = $response;
        $this->_clubHelper = $clubHelper;
        $this->_clubCollection = $clubCollection;
        $this->_groupCollection = $groupCollection;
        $this->storeManager = $storeManager;
    }
    /**
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ActionInterface
     */
    public function match(RequestInterface $request)
    {
        $_clubHelper = $this->_clubHelper;
        if (!$this->dispatched) {
            $urlKey = trim($request->getPathInfo(), '/');
            $origUrlKey = $urlKey;
            /** @var Object $condition */
            $condition = new DataObject(['url_key' => $urlKey, 'continue' => true]);
            $this->eventManager->dispatch(
                'comave_club_controller_router_match_before',
                ['router' => $this, 'condition' => $condition]
                );
            $urlKey = $condition->getUrlKey();
            if ($condition->getRedirectUrl()) {
                $this->response->setRedirect($condition->getRedirectUrl());
                $request->setDispatched(true);
                return $this->actionFactory->create(
                    'Magento\Framework\App\Action\Redirect',
                    ['request' => $request]
                    );
            }
            if (!$condition->getContinue()) {
                return null;
            }
            $route = $_clubHelper->getConfig('general_settings/route');
            $urlKeyArr = explode(".",$urlKey);
            if(count($urlKeyArr) > 1) {
                $urlKey = $urlKeyArr[0];
            }
            $routeArr = explode(".",$route);
            if(count($routeArr) > 1) {
                $route = $routeArr[0];
            }
            if( $route !='' && $urlKey == $route )
            {
                $request->setModuleName('comave_club')
                ->setControllerName('index')
                ->setActionName('index');
                $request->setAlias(Url::REWRITE_REQUEST_PATH_ALIAS, $urlKey);
                $this->dispatched = true;
                return $this->actionFactory->create(
                    'Magento\Framework\App\Action\Forward',
                    ['request' => $request]
                    );
            }
            $url_prefix = $_clubHelper->getConfig('general_settings/url_prefix');
            $url_suffix = $_clubHelper->getConfig('general_settings/url_suffix');
            $url_prefix = $url_prefix?$url_prefix:$route;
            $identifiers = explode('/',$urlKey);

            // SEARCH PAGE
            if(count($identifiers)==2 && $url_prefix == $identifiers[0] && $identifiers[1]=='search'){
                $request->setModuleName('comave_club')
                ->setControllerName('search')
                ->setActionName('result');
                $request->setAlias(\Magento\Framework\Url::REWRITE_REQUEST_PATH_ALIAS, $origUrlKey);
                $request->setDispatched(true);
                $this->dispatched = true;
                return $this->actionFactory->create(
                    'Magento\Framework\App\Action\Forward',
                    ['request' => $request]
                    );
            }
            //Check Group Url
            if( (count($identifiers) == 2 && $identifiers[0] == $url_prefix) || (trim($url_prefix) == '' && count($identifiers) == 1)){
                $clubUrl = '';
                if(trim($url_prefix) == '' && count($identifiers) == 1){
                    $clubUrl = str_replace($url_suffix, '', $identifiers[0]);
                }
                if(count($identifiers) == 2){
                    $clubUrl = str_replace($url_suffix, '', $identifiers[1]);
                }
                if ($clubUrl) {
                    $group = $this->_groupCollection->getCollection()
                    ->addFieldToFilter('status', array('eq' => 1))
                    ->addFieldToFilter('url_key', array('eq' => $clubUrl))
                    ->getFirstItem();

                    if($group && $group->getId()){
                        $request->setModuleName('comave_club')
                        ->setControllerName('group')
                        ->setActionName('view')
                        ->setParam('group_id', $group->getId());
                        $request->setAlias(\Magento\Framework\Url::REWRITE_REQUEST_PATH_ALIAS, $origUrlKey);
                        $request->setDispatched(true);
                        $this->dispatched = true;
                        return $this->actionFactory->create(
                            'Magento\Framework\App\Action\Forward',
                            ['request' => $request]
                            );
                    } else {
                        $club = $this->_clubCollection->getCollection()
                                ->addFieldToFilter('status', array('eq' => 1))
                                ->addFieldToFilter('url_key', array('eq' => $clubUrl))
                                ->addStoreFilter([0,$this->storeManager->getStore()->getId()])
                                ->getFirstItem();

                        if($club && $club->getId()){
                            $request->setModuleName('comave_club')
                            ->setControllerName('club')
                            ->setActionName('view')
                            ->setParam('club_id', $club->getId());
                            $request->setAlias(\Magento\Framework\Url::REWRITE_REQUEST_PATH_ALIAS, $origUrlKey);
                            $request->setDispatched(true);
                            $this->dispatched = true;
                            return $this->actionFactory->create(
                                'Magento\Framework\App\Action\Forward',
                                ['request' => $request]
                                );
                        }

                    }
                }
            }
            $request->setAlias(\Magento\Framework\Url::REWRITE_REQUEST_PATH_ALIAS, $origUrlKey);
            $request->setDispatched(true);
            $this->dispatched = true;
            return $this->actionFactory->create(
                'Magento\Framework\App\Action\Forward',
                ['request' => $request]
                );
        }
    }
}