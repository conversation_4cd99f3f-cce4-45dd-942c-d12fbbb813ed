<?php
declare(strict_types=1);

namespace Comave\Club\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Club interface.
 * @api
 */
interface ClubInterface extends ExtensibleDataInterface
{
    const int STATUS_ENABLED = 1;
    const int STATUS_DISABLED = 0;
    const string CLUB_ID = 'club_id';
    const string CLUB_NAME = 'name';
    const string CLUB_DESCRIPTION = 'description';
    const string CLUB_SUBTITLE = 'subtitle';
    const string CLUB_URL_KEY = 'url_key';
    const string CLUB_IMAGE = 'image';
    const string CLUB_LOGO = 'clogo';
    const string CLUB_STATUS = 'status';
    const string CLUB_POSITION = 'position';
    const string CLUB_UNIQUE_ID = 'uniqueid';
    const string CLUB_BANNER = 'club_banner';
    const string CLUB_WATERMARK_IMAGE = 'club_watermark_image';
    const string CLUB_PREFIX = 'club_prefix';
    /**
     * Get club id
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set club id
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * Get club name
     *
     * @return string|null
     */
    public function getName(): string;

    /**
     * Set club name
     *
     * @param string $name
     * @return $this
     */
    public function setName(string $name): self;

    /**
     * Get URL key
     *
     * @return string|null
     */
    public function getUrlKey();

    /**
     * Set URL key
     *
     * @param string $urlKey
     * @return $this
     */
    public function setUrlKey(string $urlKey): self;

    /**
     * Get image
     *
     * @return string|null
     */
    public function getImage();

    /**
     * Set image
     *
     * @param string $image
     * @return $this
     */
    public function setImage(string $image): self;

    /**
     * Get clogo
     *
     * @return string|null
     */
    public function getClogo(): string;

    /**
     * Set clogo
     *
     * @param string $clogo
     * @return $this
     */
    public function setClogo($clogo): self;

    /**
     * Get status
     *
     * @return int|null
     */
    public function getStatus(): int;

    /**
     * Set status
     *
     * @param int $status
     * @return $this
     */
    public function setStatus(int $status): self;

    /**
     * Retrieve available statuses
     *
     * @return array
     */
    public function getAvailableStatuses();

    /**
     * Check if club identifier exists for specific store
     *
     * @param string $identifier
     * @param int $storeId
     * @return int
     * @throws LocalizedException
     */
    public function checkIdentifier(string $identifier, int $storeId);

    /**
     * Get product collection
     *
     * @return AbstractDb
     */
    public function getProductCollection(): AbstractDb;

    /**
     * Get club URL
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getUrl(): string;

    /**
     * Get image URL
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getImageUrl(): string;

    /**
     * Load club by name
     *
     * @param string $clubName
     * @return $this
     * @throws LocalizedException
     */
    public function loadByClubName($clubName);

    /**
     * Save product
     *
     * @param string $productId
     * @return $this
     */
    public function saveProduct($productId);

    /**
     * Delete clubs by product
     *
     * @param string $productId
     * @return $this
     */
    public function deleteClubsByProduct($productId);

    /**
     * Get clogo URL
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getClogoUrl(): string;

    /**
     * Get uniquie id
     *
     * @return string
     */
    public function getUniqueId(): string;

    /**
     * Set unique id
     *
     * @param string $uniqueId
     * @return $this
     */
    public function setUniqueId(string $uniqueId): self;

    /**
     * Get banner
     *
     * @return string
     */
    public function getClubBanner(): string;

    /**
     * Set club banner
     *
     * @param string $bannerUrl
     * @return $this
     */
    public function setClubBanner(string $bannerUrl): self;

    /**
     * Get club watermark image
     *
     * @return string
     */
    public function getClubWatermarkImage(): string;
}
