<?php

declare(strict_types=1);

namespace Comave\Club\Api;

use Comave\Club\Api\Data\ClubInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Club repository interface.
 * @api
 */
interface ClubRepositoryInterface
{
    /**
     * Save club.
     *
     * @param ClubInterface $club
     * @return ClubInterface
     * @throws CouldNotSaveException
     */
    public function save(ClubInterface $club): ClubInterface;

    /**
     * Retrieve club.
     *
     * @param int $clubId
     * @return ClubInterface
     * @throws NoSuchEntityException
     */
    public function get(int $clubId): ClubInterface;

    /**
     * Retrieve club by unique id
     *
     * @param string $uniqueId
     * @return ClubInterface
     * @throws NoSuchEntityException
     */
    public function getByUniqueId(string $uniqueId): ClubInterface;

    /**
     * Retrieve clubs matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface;

    /**
     * Delete club.
     *
     * @param ClubInterface $club
     * @return bool true on success
     * @throws CouldNotDeleteException
     */
    public function delete(ClubInterface $club): bool;

    /**
     * Delete club by ID.
     *
     * @param int $clubId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function deleteById(int $clubId): bool;
}
