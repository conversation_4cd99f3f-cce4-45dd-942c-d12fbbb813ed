<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Club\Helper;

use Comave\Club\Model\Cookie\Custom;

/**
 * @deprecared
 * Club Data Helper
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * Group Collection
     */
    protected $_groupCollection;

    /** @var \Magento\Store\Model\StoreManagerInterface */
    protected $_storeManager;

    /**
     * Club config node per website
     *
     * @var array
     */
    protected $_config = [];

    /**
     * Template filter factory
     *
     * @var \Magento\Catalog\Model\Template\Filter\Factory
     */
    protected $_templateFilterFactory;

    /**
     * @var \Magento\Cms\Model\Template\FilterProvider
     */
    protected $_filterProvider;

    protected $_request;
    private Custom $_myCookie;

    public function __construct(
        \Magento\Framework\App\Helper\Context      $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Comave\Club\Model\Group                 $groupCollection,
        \Magento\Cms\Model\Template\FilterProvider $filterProvider,
        Custom                                     $myCookie
    )
    {
        parent::__construct($context);
        $this->_storeManager = $storeManager;
        $this->_groupCollection = $groupCollection;
        $this->_filterProvider = $filterProvider;
        $this->_myCookie = $myCookie;
        $this->_request = $context->getRequest();
    }

    public function getGroupList()
    {
        $result = array();
        $collection = $this->_groupCollection->getCollection()
            ->addFieldToFilter('status', '1');
        foreach ($collection as $clubGroup) {
            $result[$clubGroup->getId()] = $clubGroup->getName();
        }
        return $result;
    }

    /**
     * Return club config value by key and store
     *
     * @param string $key
     * @param \Magento\Store\Model\Store|int|string $store
     * @return string|null
     */
    public function getConfig($key, $store = null)
    {
        $store = $this->_storeManager->getStore($store);
        $websiteId = $store->getWebsiteId();

        $result = $this->scopeConfig->getValue(
            'comave_club/' . $key,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store);
        return $result;
    }

    public function filter($str)
    {
        $html = $this->_filterProvider->getPageFilter()->filter($str);
        return $html;
    }

    public function getSearchFormUrl()
    {
        $url = $this->_storeManager->getStore()->getBaseUrl();
        $url_prefix = $this->getConfig('general_settings/url_prefix');
        $url_suffix = $this->getConfig('general_settings/url_suffix');
        $urlPrefix = '';
        if ($url_prefix) {
            $urlPrefix = $url_prefix . '/';
        }
        return $url . $urlPrefix . 'search';
    }

    public function getSearchKey()
    {
        return $this->_request->getParam('s');
    }

    public function setCookieValue($value, $duration)
    {
        return $this->_myCookie->set($value, $duration);
    }

    public function getCookieValue()
    {
        return $this->_myCookie->get();
    }

    public function getBaseUrl()
    {
        return $this->_storeManager->getStore()->getBaseUrl();
    }

    public function getMediaUrl()
    {
        return $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
    }
}
