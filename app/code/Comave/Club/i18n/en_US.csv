"Manage Clubs","Manage Clubs"
"Add New Club","Add New Club"
"Save Club","Save Club"
"Save and Continue Edit","Save and Continue Edit"
"Delete Club","Delete Club"
"Edit Club '%1'","Edit Club '%1'"
"New Club","New Club"
"Page Layout","Page Layout"
Layout,Layout
"Layout Update XML","Layout Update XML"
Design,Design
"Club Information","Club Information"
"Club Name","Club Name"
"URL Key","URL Key"
"Empty to auto create url key","Empty to auto create url key"
"Club Group","Club Group"
Image,Image
Clogo,Clogo
Description,Description
"Store View","Store View"
Position,Position
Status,Status
"Page Status","Page Status"
"Meta Data","Meta Data"
"Page Title","Page Title"
Keywords,Keywords
"Meta Keywords","Meta Keywords"
"Meta Description","Meta Description"
ID,ID
Name,Name
Type,Type
"Attribute Set","Attribute Set"
Visibility,Visibility
SKU,SKU
Price,Price
Products,Products
Preview,Preview
"Manage Club Groups","Manage Club Groups"
"Add New Club Group","Add New Club Group"
"Save Group","Save Group"
"Delete Group","Delete Group"
"Edit Group '%1'","Edit Group '%1'"
"New Group","New Group"
"Group Information","Group Information"
"Group Name","Group Name"
"Show In Sidebar","Show In Sidebar"
"WYSIWYG Editor","WYSIWYG Editor"
Title,Title
Home,Home
"Go to Home Page","Go to Home Page"
"The club has been deleted.","The club has been deleted."
"We can't find a club to delete.","We can't find a club to delete."
Club,Club
"This club no longer exits. ","This club no longer exits. "
"Edit Club","Edit Club"
Clubs,Clubs
"Please correct the data sent.","Please correct the data sent."
"Something went wrong while saving the page.","Something went wrong while saving the page."
"A total of %1 record(s) have been deleted.","A total of %1 record(s) have been deleted."
"A total of %1 record(s) have been disabled.","A total of %1 record(s) have been disabled."
"A total of %1 record(s) have been enabled.","A total of %1 record(s) have been enabled."
"You saved this club.","You saved this club."
"Something went wrong while saving the club.","Something went wrong while saving the club."
"Club Groups","Club Groups"
"URL key already exists.","URL key already exists."
"You saved this group.","You saved this group."
Enabled,Enabled
Disabled,Disabled
List,List
Grid,Grid
No,No
Yes,Yes
"URL key for specified store already exists.","URL key for specified store already exists."
"'URL key already exists.' .","'URL key already exists.' ."
"Owl Carousel","Owl Carousel"
"Bootstrap Carousel","Bootstrap Carousel"
"All Store Views","All Store Views"
Edit,Edit
"We can't find clubs matching the selection.","We can't find clubs matching the selection."
"We can't find club matching the selection.","We can't find club matching the selection."
Items,Items
of,of
"%1 Item","%1 Item"
"%1 Items","%1 Items"
Previous,Previous
Next,Next
"Insert Widget...","Insert Widget..."
"Comave Club List","Comave Club List"
"Get club list","Get club list"
"Select Static Block","Select Static Block"
"Pretext HTML","Pretext HTML"
"Number of Club to Display","Number of Club to Display"
"Number Item per Column","Number Item per Column"
"Show Club Name","Show Club Name"
"Addtion Class","Addtion Class"
"Carousel Layout","Carousel Layout"
"Number Columns On Page (Phones)","Number Columns On Page (Phones)"
"Show number items when screen size bellow 480px","Show number items when screen size bellow 480px"
"Number Columns On Page (Phones to Small tablets)","Number Columns On Page (Phones to Small tablets)"
"Show number items when screen size between 641px and 480px","Show number items when screen size between 641px and 480px"
"Number Columns On Page (Phones to tablets)","Number Columns On Page (Phones to tablets)"
"Show number items when screen size between 768px and 641px","Show number items when screen size between 768px and 641px"
"Number Columns On Page (Portrait tablets)","Number Columns On Page (Portrait tablets)"
"Show number items when screen size between 979px and 769px","Show number items when screen size between 979px and 769px"
"Number Columns On Page (Default)","Number Columns On Page (Default)"
"Show number items when screen size between 1199px and 980px","Show number items when screen size between 1199px and 980px"
"Number Columns On Page (Large display)","Number Columns On Page (Large display)"
"Show number items when screen size 1200px and up","Show number items when screen size 1200px and up"
Autoplay,Autoplay
"Autoplay Timeout","Autoplay Timeout"
"Item Per Page","Item Per Page"
"Number Column on Desktop","Number Column on Desktop"
"Number Column on Tablets","Number Column on Tablets"
"Number Column on Phones","Number Column on Phones"
Interval,Interval
RTL,RTL
Loop,Loop
"Show dots navigations","Show dots navigations"
"Show next/prev buttons.","Show next/prev buttons."
"Nav Prev Text","Nav Prev Text"
"Nav Next Text","Nav Next Text"
Template,Template
Delete,Delete
"Delete items","Delete items"
"Are you sure you wan't to delete selected items?","Are you sure you wan't to delete selected items?"
Disable,Disable
Enable,Enable
