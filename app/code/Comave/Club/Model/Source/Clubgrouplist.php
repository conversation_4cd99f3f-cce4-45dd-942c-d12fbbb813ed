<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Model\Source;

class Clubgrouplist implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * @var \Comave\Club\Model\Group
     */
    protected  $_group;
    
    /**
     * 
     * @param \Comave\Club\Model\Group $group
     */
    public function __construct(
        \Comave\Club\Model\Group $group
        ) {
        $this->_group = $group;
    }

    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
        $groups = $this->_group->getCollection()
        ->addFieldToFilter('status', '1');
        $groupList = array();
        foreach ($groups as $group) {
            $groupList[] = array('label' => $group->getName(),
                'value' => $group->getId());
        }
        return $groupList;
    }
}
