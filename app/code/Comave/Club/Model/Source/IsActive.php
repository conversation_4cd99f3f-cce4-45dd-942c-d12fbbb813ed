<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Model\Source;
use Magento\Framework\Data\OptionSourceInterface;

class IsActive implements OptionSourceInterface
{
	/**
	 * @var \Comave\Club\Model\Club
	 */
	protected $clubModel;

	/**
     * Constructor
     *
     * @param \Comave\Club\Model\Club $clubModel
     */
	public function __construct(\Comave\Club\Model\Club $clubModel)
	{
		$this->clubModel = $clubModel;
	}

	/**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        $options[] = ['label' => '', 'value' => ''];
        $availableOptions = $this->clubModel->getAvailableStatuses();

        foreach ($availableOptions as $key => $value) {
            $options[] = [
                'label' => $value,
                'value' => $key,
            ];
        }
 
        return $options;
    }
}