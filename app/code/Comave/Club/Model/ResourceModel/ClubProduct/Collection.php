<?php
declare(strict_types=1);

namespace Comave\Club\Model\ResourceModel\ClubProduct;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Comave\Club\Model\ClubProduct;
use Comave\Club\Model\ResourceModel\ClubProduct as ClubProductResource;

class Collection extends AbstractCollection
{
    protected function _construct()
    {
        $this->_init(ClubProduct::class, ClubProductResource::class);
    }
}
