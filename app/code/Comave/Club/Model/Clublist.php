<?php
declare(strict_types=1);

namespace Comave\Club\Model;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Comave\Club\Model\ResourceModel\Club\CollectionFactory;
use Magento\Framework\Data\Collection;

class Clublist extends AbstractSource
{
    public function __construct(
        protected readonly Club $club,
        private readonly CollectionFactory $clubFactory
    ) {
    }

    /**
     * Get default collection
     **/
    public function getDefaultCollection()
    {
        return $this->clubFactory->create();
    }

    /**
     * Get Gift Card available templates
     *
     * @return array
     */
    public function getAvailableTemplate(): array
    {
        $clubs = $this->club->getCollection()
        ->addFieldToFilter('status', '1');
        $listClub = array();
        foreach ($clubs as $club) {
            $listClub[] = array('label' => $club->getName(),
                'value' => $club->getId());
        }
        return $listClub;
    }

    /**
     * Get model option as array
     *
     * @param bool $withEmpty
     * @return array
     */
    public function getAllOptions($withEmpty = true): array
    {
        $options = $this->getAvailableTemplate();

        if ($withEmpty) {
            array_unshift($options, array(
                'value' => '',
                'label' => '-- Please Select --',
                ));
        }
        return $options;
    }

    public function getActiveClubs(): array
    {
        $collection = $this->getDefaultCollection();

        return $collection->getItems();
    }
}
