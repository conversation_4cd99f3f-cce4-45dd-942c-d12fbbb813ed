<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Model\Config\Source;
use AllowDynamicProperties;

#[AllowDynamicProperties] class Staticblock implements \Magento\Framework\Option\ArrayInterface
{
	protected  $_blockModel;

    /**
     * @param \Magento\Cms\Model\Block $blockModel
     */
    public function __construct(
    	\Magento\Cms\Model\Block $blockModel
    	) {
    	$this->_groupModel = $blockModel;
    }

    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
    	$collection = $this->_groupModel->getCollection();
    	$blocks = array();
    	foreach ($collection as $_block) {
    		$blocks[] = [
    		'value' => $_block->getId(),
    		'label' => addslashes($_block->getTitle())
    		];
    	}
        $blocks[] = [
                'value' => 'pretext_html',
                'label' => 'Pretext HTML'];
        array_unshift($blocks, array(
                'value' => '',
                'label' => '-- Please Select --',
                ));
        return $blocks;
    }
}
