<?php
declare(strict_types=1);

namespace Comave\Club\Model\Repository;

use Comave\Club\Api\ClubRepositoryInterface;
use Comave\Club\Api\Data\ClubInterface;
use Comave\Club\Model\ClubFactory;
use Comave\Club\Model\ResourceModel\Club as ClubResource;
use Comave\Club\Model\ResourceModel\Club\CollectionFactory as ClubCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SearchResultsInterfaceFactory;
use Magento\Framework\Data\Collection;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class ClubRepository implements ClubRepositoryInterface
{
    public function __construct(
        private readonly ClubResource $resource,
        private readonly ClubFactory $clubFactory,
        private readonly ClubCollectionFactory $clubCollectionFactory,
        private readonly SearchResultsInterfaceFactory $searchResultsFactory,
        private readonly CollectionProcessorInterface $collectionProcessor
    ) {
    }

    /**
     * @inheritDoc
     */
    public function save(ClubInterface $club): ClubInterface
    {
        try {
            $this->resource->save($club);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(
                __('Could not save the club: %1', $exception->getMessage()),
                $exception
            );
        }
        return $club;
    }

    /**
     * @inheritDoc
     */
    public function get(int $clubId): ClubInterface
    {
        $club = $this->clubFactory->create();
        $this->resource->load($club, $clubId);
        if (!$club->getId()) {
            throw new NoSuchEntityException(__('Club with id "%1" does not exist.', $clubId));
        }

        return $club;
    }

    /**
     * @inheritDoc
     */
    public function getByUniqueId(string $uniqueId): ClubInterface
    {
        $collection = $this->clubCollectionFactory->create();
        $collection->addFieldToFilter(ClubInterface::CLUB_UNIQUE_ID, $uniqueId);
        $club = $collection->getFirstItem();

        if (!$club->getId()) {
            throw new NoSuchEntityException(__('Club with unique ID "%1" does not exist.', $uniqueId));
        }

        return $club;
    }

    /**
     * @inheritDoc
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface
    {
        $collection = $this->clubCollectionFactory->create();

        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(ClubInterface $club): bool
    {
        try {
            $this->resource->delete($club);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(
                __('Could not delete the club: %1', $exception->getMessage())
            );
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById(int $clubId): bool
    {
        return $this->delete($this->get($clubId));
    }

    /**
     * Get active clubs ordered by position
     *
     * @return ClubResource\Collection
     */
    public function getActiveClubs(): Collection
    {
        $collection = $this->clubCollectionFactory->create();
        $collection
            ->addFieldToFilter(ClubInterface::CLUB_STATUS, ClubInterface::STATUS_ENABLED)
            ->setOrder(ClubInterface::CLUB_POSITION, Collection::SORT_ORDER_ASC);

        return $collection;
    }
}
