<?php
declare(strict_types=1);

namespace Comave\Club\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class ConfigProvider
{
    public const XML_PATH_CLUB_ENABLED = 'comave_club/general_settings/enabled';
    public const XML_PATH_CLUB_URL_PREFIX = 'comave_club/general_settings/url_prefix';
    public const XML_PATH_CLUB_URL_SUFFIX = 'comave_club/general_settings/url_suffix';
    public const XML_PATH_CLUB_URL = 'comave_club/general_settings/route';
    public const XML_PATH_CLUB_PAGE_TITLE = 'comave_club/club_list_page/page_title';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    public function isEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_CLUB_ENABLED,
            ScopeInterface::SCOPE_STORES,
            $storeId
        );
    }

    /**
     * @param $key
     * @param $store
     * @return mixed
     * @deprecared
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getConfig($key, $store = null)
    {
        $store = $this->storeManager->getStore($store);
        $result = $this->scopeConfig->getValue(
            'comave_club/'.$key,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );

        return $result;
    }

    public function getUrlPrefix(?int $storeId = null): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CLUB_URL_PREFIX,
            ScopeInterface::SCOPE_STORES,
            $storeId
        );
    }

    public function getUrlSuffix(?int $storeId = null): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CLUB_URL_SUFFIX,
            ScopeInterface::SCOPE_STORES,
            $storeId
        );
    }

    public function getClubUrl(?int $storeId = null): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CLUB_URL,
            ScopeInterface::SCOPE_STORES,
            $storeId
        );
    }

    public function getClubPageTitle(?int $storeId = null): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CLUB_PAGE_TITLE,
            ScopeInterface::SCOPE_STORES,
            $storeId
        );
    }
}
