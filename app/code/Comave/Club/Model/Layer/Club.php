<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Model\Layer;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\Resource;

class Club extends \Magento\Catalog\Model\Layer
{
    /**
     * Retrieve current layer product collection
     *
     * @return \Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    public function getProductCollection()
    {
    	$club = $this->getCurrentClub();
    	if(isset($this->_productCollections[$club->getId()])){
    		$collection = $this->_productCollections;
    	}else{
    		$collection = $club->getProductCollection();
    		$this->prepareProductCollection($collection);
            $this->_productCollections[$club->getId()] = $collection;
    	} 
    	return $collection;
    }

    /**
     * Retrieve current category model
     * If no category found in registry, the root will be taken
     *
     * @return \Magento\Catalog\Model\Category
     */
    public function getCurrentClub()
    {
    	$club = $this->getData('current_club');
    	if ($club === null) {
    		$club = $this->registry->registry('current_club');
    		if ($club) {
    			$this->setData('current_club', $club);
    		}
    	}
    	return $club;
    }
}