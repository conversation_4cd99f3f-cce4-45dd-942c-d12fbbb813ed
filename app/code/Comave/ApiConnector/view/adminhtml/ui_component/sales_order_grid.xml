<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <columns name="sales_order_columns">
        <actionsColumn name="club_name" class="Comave\ApiConnector\Ui\Component\Listing\Column\ClubName">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Club Name</item>
                    <item name="sortOrder" xsi:type="number">160</item>
                </item>
            </argument>
        </actionsColumn>
        <actionsColumn name="club_commission" class="Comave\ApiConnector\Ui\Component\Listing\Column\ClubComm">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Club Commission</item>
                    <item name="sortOrder" xsi:type="number">180</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>
