<?php

namespace Comave\ApiConnector\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Ui\Component\Listing\Columns\Column;
use \Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\UrlInterface;

class ClubComm extends Column
{
    protected $_orderRepository;

    /** @var UrlInterface */
    protected $_urlBuilder;

    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    protected $_orderFactory;

    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        OrderRepositoryInterface $orderRepository,
        UrlInterface $urlBuilder,
        \Magento\Sales\Model\OrderFactory $orderFactory,
        array $components = [],
        array $data = []
    ) {
        $this->_orderRepository = $orderRepository;
        $this->_urlBuilder = $urlBuilder;
        $this->_orderFactory    = $orderFactory;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as & $item) {
                $order = $this->_orderFactory->create()->loadByIncrementId($item['increment_id']);

                if ($order->getId()) {
                    $ClubComm = $order->getData("club_commission");
                    $item[$this->getData('name')] = $ClubComm;
                }
            }
        }

        return $dataSource;
    }
}
