<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="marketplace_orders">
		<column name="order_state" nullable="true" xsi:type="varchar" comment="Order State" length="255"/>
	</table>
	<table name="sales_order">
		<column name="club_name" nullable="true" xsi:type="varchar" comment="Sports Club Identifier" length="255"/>
		<column name="club_commission" nullable="true" xsi:type="varchar" comment="Sports Club Commission" length="255"/>
		<column name="order_approval_enabled" nullable="false" xsi:type="boolean" comment="Order Approval For Restaurant" default="0"/>
		<column name="order_placement_time" nullable="true" xsi:type="timestamp" comment="Order Placement Time"/>
	</table>
</schema>
