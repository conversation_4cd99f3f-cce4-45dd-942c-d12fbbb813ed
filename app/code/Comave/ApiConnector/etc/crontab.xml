<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
	<group id="comave_group">
        <!-- Disabled until further notice
		<job name="comave_apiconnector_productsync" instance="Comave\ApiConnector\Cron\ProductSync" method="execute">
			<schedule>15 */1 * * *</schedule>
		</job>
		<job name="comave_apiconnector_sellersync" instance="Comave\ApiConnector\Cron\SellerSync" method="execute">
			<schedule>30 */1 * * *</schedule>
		</job>
		<job name="comave_apiconnector_currencyratesync" instance="Comave\ApiConnector\Cron\CurrencyRateSync" method="execute">
			<schedule>*/5 * * * *</schedule>
		</job>
		<job name="comave_apiconnector_deleteprod" instance="Comave\ApiConnector\Cron\DeleteProd" method="execute">
			<schedule>* * * * *</schedule>
		</job>
		<job name="comave_apiconnector_createshipment" instance="Comave\ApiConnector\Cron\CreateShipment" method="execute">
			<schedule>*/30 * * * *</schedule>
		</job>
		<job name="comave_apiconnector_cancel_order_pending" instance="Comave\ApiConnector\Cron\CancelOrderPending" method="execute">
            <schedule>*/5 * * * *</schedule>
        </job>
         -->
	</group>
</config>
