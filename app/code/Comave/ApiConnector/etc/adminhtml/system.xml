<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<tab id="api_connect" sortOrder="300" translate="label">
			<label>WallPost Api Connect</label>
		</tab>
		<section id="settings" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
			<label>Settings</label>
			<tab>api_connect</tab>
			<resource>Comave_ApiConnector::config_comave_apiconnector</resource>
			<group id="general" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
				<label>General</label>
				<field id="site_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="text">
					<label>Site Url (Endpoint)</label>
					<comment/>
				</field>
				<field id="availability" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label" type="select">
					<label>Availability</label>
					<comment/>
					<source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
				</field>
				<field id="user" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70" translate="label" type="text">
					<label>User Name / Client ID</label>
					<comment/>
				</field>
				<field id="password" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80" translate="label" type="obscure">
					<label>Password / Client Secret</label>
					<comment/>
					<backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
					<config_path>settings/general/password</config_path>
				</field>
				<field id="access_key" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>web service access key</label>
					<comment/>
				</field>
			</group>
			<group id="ushop_settings" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label">
				<label>Ushop settings</label>
				<field id="site_url" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label" type="text">
					<label>Site Url (Endpoint)</label>
					<comment/>
				</field>
				<field id="availability" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="50" translate="label" type="select">
					<label>Availability</label>
					<comment/>
					<source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
				</field>
				<field id="account_no" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>Account Number</label>
					<comment/>
				</field>
				<field id="app_type" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>App Type</label>
					<comment/>
				</field>
				<field id="username" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>User Name</label>
					<comment/>
				</field>
				<field id="password" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type='obscure'>
					<label>Password</label>
					<comment/>
					<backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
				</field>
				<field id="device_uid" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>Device User Id</label>
					<comment/>
				</field>
				<field id="environment" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>Environment</label>
					<comment/>
				</field>
				<!-- <field id="access_key" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90" translate="label" type="text">
					<label>web service access key</label>
					<comment/>
				</field> -->
				<field id="company_id" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label>Company Id</label>
				</field>
			</group>
			<group id="commission_settings" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" translate="label">
				<label>Commission settings</label>
				<field id="service_charge" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label>Lix Service Charge</label>
				</field>
				<field id="sports_club_comm" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="100" translate="label" type="text">
					<label>Sports Club Commission</label>
				</field>
			</group>
		</section>
		<section id="auto_invoice" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
			<label>Auto Invoice</label>
			<tab>api_connect</tab>
			<resource>Comave_ApiConnector::config_comave_apiconnector</resource>
			<group id="general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
				<label>General</label>
				<field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Enable/Disable</label>
					<comment/>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
		<section id="auto_shipment" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
			<label>Auto shipment</label>
			<tab>api_connect</tab>
			<resource>Comave_ApiConnector::config_comave_apiconnector</resource>
			<group id="general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
				<label>General</label>
				<field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Enable/Disable</label>
					<comment/>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
