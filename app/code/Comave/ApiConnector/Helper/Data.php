<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Helper;

use AllowDynamicProperties;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\Message\ManagerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Catalog\Model\Indexer\Product\Price\Processor;
use Magento\Catalog\Model\Product\Action as ProductAction;
use Webkul\Marketplace\Helper\Data as MarketplaceHelperData;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as MpProductCollection;

#[AllowDynamicProperties]
class Data extends AbstractHelper
{
    const URL_PATH_KEY = 'settings/general/odata_base_url';
    const URL_USER_KEY = 'settings/general/user';
    const URL_ACCESS_KEY = 'settings/general/access_key';
    const URL_AVAILABLE = 'settings/general/availability';
    const URL_SITE_KEY = 'settings/general/site_url';
    const AUTO_INVOICE = 'auto_invoice/general/enable';
    const AUTO_SHIP = 'auto_shipment/general/enable';
    const LIX_CHARGE = 'settings/commission_settings/service_charge';
    const CLUB_COMMISSION = 'settings/commission_settings/sports_club_comm';
    const USHOP_ACCESS_KEY = 'settings/ushop_settings/access_key';
    const USHOP_AVAILABLE = 'settings/ushop_settings/availability';
    const USHOP_SITE_KEY = 'settings/ushop_settings/site_url';
    const USHOP_ACT_ID = 'settings/ushop_settings/account_no';
    const USHOP_APP_TYPE = 'settings/ushop_settings/app_type';
    const USHOP_USR_NAME = 'settings/ushop_settings/username';
    const USHOP_USR_PASS = 'settings/ushop_settings/password';
    const USHOP_DEVICE_ID = 'settings/ushop_settings/device_uid';
    const USHOP_ENV = 'settings/ushop_settings/environment';
    const USHOP_COMP_ID = 'settings/ushop_settings/company_id';
    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    protected $encryptor;
    /**
     * @var \Magento\Catalog\Api\ProductAttributeRepositoryInterface
     */
    protected $attributeRepository;
    /**
     * @var \Magento\Eav\Model\Entity\Attribute\Source\TableFactory
     */
    protected $tableFactory;
    /**
     * @var \Magento\Eav\Api\AttributeOptionManagementInterface
     */
    protected $attributeOptionManagement;
    /**
     * @var \Magento\Eav\Api\Data\AttributeOptionLabelInterfaceFactory
     */
    protected $optionLabelFactory;
    /**
     * @var \Magento\Eav\Api\Data\AttributeOptionInterfaceFactory
     */
    protected $optionFactory;
    /**
     * @var array
     */
    protected $attributeValues;
    /**
     * @var MarketplaceHelperData
     */
    protected $_marketplaceHelperData;
    /**
     * @var \Magento\Catalog\Model\Indexer\Product\Price\Processor
     */
    protected $_productPriceIndexerProcessor;
    /**
     * @var ProductAction
     */
    protected $productAction;
    protected $storeManager;
    protected $objectManager;

    public function __construct(
        Context                                          $context,
        MarketplaceHelperData                            $marketplaceHelperData,
        \Webkul\Marketplace\Model\ProductFactory         $mpProductFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime      $date,
        MpProductCollection                              $mpProductCollectionFactory,
        ProductAction                                    $productAction = null,
        Processor                                        $productPriceIndexerProcessor,
        \Magento\Store\Model\StoreManagerInterface       $storeManager,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        private readonly ManagerInterface                $messageManager
    )
    {
        $this->_marketplaceHelperData = $marketplaceHelperData;
        $this->_mpProductFactory = $mpProductFactory;
        $this->_date = $date;
        $this->_mpProductCollectionFactory = $mpProductCollectionFactory;
        $this->productAction = $productAction ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(ProductAction::class);
        $this->_productPriceIndexerProcessor = $productPriceIndexerProcessor;
        $this->_storeManager = $storeManager;
        $this->encryptor = $encryptor;

        parent::__construct($context);
    }

    public function getAvailable($storeId = null)
    {
        return $this->getConfigValue(self::URL_AVAILABLE, $storeId);
    }

    public function getConfigValue($field, $storeId = null)
    {
        return $this->scopeConfig->getValue(
            $field, ScopeInterface::SCOPE_STORE, $storeId
        );
    }

    public function getAutoShip($storeId = null)
    {
        return $this->getConfigValue(self::AUTO_SHIP, $storeId);
    }

    public function getAutoInvoice($storeId = null)
    {
        return $this->getConfigValue(self::AUTO_INVOICE, $storeId);
    }

    public function getLixCharge($storeId = null)
    {
        return $this->getConfigValue(self::LIX_CHARGE, $storeId);
    }

    public function getSportsClubComm($storeId = null)
    {
        return $this->getConfigValue(self::CLUB_COMMISSION, $storeId);
    }

    public function getUshopAvailable($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_AVAILABLE, $storeId);
    }

    public function getUshopToken($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_ACCESS_KEY, $storeId);
    }

    /**
     * @deprecated
     * Get Ushop Customer
     *
     * @param string $email
     * @return array
     */
    public function getUshopTraveller($email)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/notifyTrav.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In getUshopTraveller " . $email);

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();

        $company_id = $this->getUshopCompId();
        $headerpost = array(
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'Authorization:' . $access_token
        );

        $call_url = $url . "companies/" . $company_id . "/comave/travellers";

        $postfields = array();
        $postfields['traveller_email'] = $email;

        $postData = json_encode($postfields);
        $response = [];
        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_COOKIESESSION, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headerpost);

            $response = json_decode(curl_exec($ch), TRUE);
            $logger->info(print_r($response, true));

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    public function getUshopSiteUrl($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_SITE_KEY, $storeId);
    }

    public function getUshopAuth()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/notifyTrav.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $url = $this->getUshopSiteUrl();
        $call_url = $url . "authorization";

        $password = $this->encryptor->decrypt($this->getUshopUsrPass());

        $postfields = array();
        $postfields['accountno'] = $this->getUshopActId();
        $postfields['apptype'] = $this->getUshopAppType();
        $postfields['username'] = $this->getUshopUsrName();
        $postfields['password'] = $password;
        $postfields['deviceuid'] = $this->getUshopDeviceId();
        $postfields['environment'] = $this->getUshopEnv();

        $postData = json_encode($postfields);

        $logger->info("Url " . $call_url);
        $logger->info(print_r($postData, true));

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8'
            ]);

            $response = json_decode(curl_exec($ch), TRUE);
            $logger->info(print_r($response, true));

            if ($response) {
                if (array_key_exists("data", $response)) {
                    $token = $response['data']['token'];
                    return $token;
                } else {
                    return false;
                }
            }

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }
    }

    public function getUshopUsrPass($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_USR_PASS, $storeId);
    }

    public function getUshopActId($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_ACT_ID, $storeId);
    }

    public function getUshopAppType($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_APP_TYPE, $storeId);
    }

    public function getUshopUsrName($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_USR_NAME, $storeId);
    }

    public function getUshopDeviceId($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_DEVICE_ID, $storeId);
    }

    public function getUshopEnv($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_ENV, $storeId);
    }

    public function getUshopCompId($storeId = null)
    {
        return $this->getConfigValue(self::USHOP_COMP_ID, $storeId);
    }

    /***
     * @deprecated
     * Set Ushop Customer
     *
     * @param $postData
     * @return array
     */
    public function setUshopTraveller($postData)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/notifyTrav.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In Here");
        $logger->info(print_r($postData, true));

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/register/notify";

        $logger->info("Url " . $call_url);
        $logger->info(print_r($postData, true));

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), TRUE);
            $logger->info(print_r($response, true));
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    /* Get Customer Availability */
    /**
     * @deprecated
     * Get Ushop Forms
     *
     * @param $cid
     * @return array
     */
    public function getUshopForms($cid)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/notifyTrav.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In Here " . $cid);

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $company_id = $this->getUshopCompId();
        $headerpost = array(
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'Authorization:' . $access_token
        );

        $call_url = $url . "companies/" . $company_id . "/comave/traveller/" . $cid . "/forms";

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_COOKIESESSION, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headerpost);

            $response = json_decode(curl_exec($ch), TRUE);
            $logger->info(print_r($response, true));

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    /* Notify Ushop Customer Registration */

    public function setSalesOrder($postdata)
    {
        return [];
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "company/salesOrder");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), TRUE);

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    /* Get Older Ushop Forms */

    public function getSiteUrl($storeId = null)
    {
        return $this->getConfigValue(self::URL_SITE_KEY, $storeId);
    }

    /* Set Sales order */

    public function getToken($storeId = null)
    {
        return $this->getConfigValue(self::URL_ACCESS_KEY, $storeId);
    }

    public function getCustomersByEmailId($cemail)
    {
        return [];
        /*
         * to be decoupled
         */
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;
        $postfields = array();
        $postfields['filter_by'] = $cemail;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "company/customers/list");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: multipart/form-data; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), TRUE);
            // print_r($response);exit;

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    public function setCustomers($custData)
    {

        return [];
        /*
         * to be decoupled
         */
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "company/customer");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $custData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), TRUE);

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    /* Get All Vendors */

    public function getVendors()
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "accounts");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorization]);

            $response = json_decode(curl_exec($ch), true);
            // echo "<pre>";
            // print_r($response);exit;

            curl_close($ch);
        } catch (\Exception $e) {
            return false;
        }
        // exit;
        return $response;
    }

    public function getRestorant()
    {
        return [];
    }

    public function getPickupOrders()
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "pickupSchedule");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorization]);

            $response = json_decode(curl_exec($ch), true);
            curl_close($ch);
        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    public function updatePickup($putData)
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "company/updatePickup");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $putData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    public function getEasyPostApi($postdata)
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "addresses");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), TRUE);
        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    public function vatrefundAmount($userId)
    {

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();

        $company_id = $this->getUshopCompId();
        $authorization = "Authorization:" . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/travellerDashboard?comave_user_id={$userId}";

        $response = null;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $raw_response = curl_exec($ch);
            $response = json_decode($raw_response, true);

            curl_close($ch);

        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    public function getCustomer($uid)
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "data/" . $uid . "/company");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorization]);

            $response = json_decode(curl_exec($ch), true);
            curl_close($ch);
        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    /* Get BC Customers */

    public function getCustProduct($uid)
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "data/" . $uid . "/products/all");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorization]);

            //$response = curl_exec($ch);
            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    public function updateSellerProd(
        $sellerId,
        $status
    )
    {
        $allStores = $this->_storeManager->getStores();

        $sellerProduct = $this->_mpProductCollectionFactory->create()
            ->addFieldToFilter(
                'seller_id',
                $sellerId
            );

        if ($sellerProduct->getSize()) {
            $productIds = $sellerProduct->getAllIds();
            $coditionArr = [];
            foreach ($productIds as $key => $id) {
                $condition = "`mageproduct_id`=" . $id;
                array_push($coditionArr, $condition);
            }
            $coditionData = implode(' OR ', $coditionArr);

            $sellerProduct->setProductData(
                $coditionData,
                ['status' => $status]
            );

            foreach ($allStores as $eachStoreId => $storeId) {
                $this->productAction->updateAttributes(
                    $productIds,
                    ['status' => $status],
                    $storeId
                );
            }

            $this->productAction->updateAttributes($productIds, ['status' => $status], 0);
            $this->_productPriceIndexerProcessor->reindexList($productIds);
        }
    }

    public function saveSellerProducts(
        $mageProductId,
        $rowId,
        $sellerId,
        $status,
        $editFlag,
        $associatedProductIds
    )
    {
        $savedIsApproved = 0;
        $sellerProductId = 0;
        $helper = $this->_marketplaceHelperData;
        if ($mageProductId) {
            $sellerProductColls = $this->_mpProductCollectionFactory->create()
                ->addFieldToFilter(
                    'mageproduct_id',
                    $mageProductId
                )->addFieldToFilter(
                    'seller_id',
                    $sellerId
                );
            foreach ($sellerProductColls as $sellerProductColl) {
                $sellerProductId = $sellerProductColl->getId();
                $savedIsApproved = $sellerProductColl->getIsApproved();
            }
            $collection1 = $this->_mpProductFactory->create()->load($sellerProductId);
            $collection1->setMageproductId($mageProductId);
            $collection1->setSellerId($sellerId);
            $collection1->setMageProRowId($rowId);
            $collection1->setStatus($status);
            $isApproved = 1;
            if ($helper->getIsProductEditApproval()) {
                $collection1->setAdminPendingNotification(2);
            }
            if (!$editFlag) {
                $collection1->setCreatedAt($this->_date->gmtDate());
                $collection1->setAdminPendingNotification(1);
                if ($helper->getIsProductApproval()) {
                    $isApproved = 0;
                }
            } elseif (!$helper->getIsProductEditApproval()) {
                $isApproved = $savedIsApproved;
            } else {
                $isApproved = 0;
            }
            $collection1->setIsApproved($isApproved);
            $collection1->setUpdatedAt($this->_date->gmtDate());
            $collection1->save();
        }
    }

    public function getBaseUrl()
    {
        return $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_WEB);
    }

    public function notSyncProduct($uid)
    {
        return [];

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "data/" . $uid . "/products/not_sync");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorization]);

            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;

    }

    public function notSyncProductStatus($putData, $uid)
    {
        return [];
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "data/" . $uid . "/sync/callback");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $putData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    public function notSyncRestStatus($putData)
    {
        return [];
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Authorization: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "company/sync/callback");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $putData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }

        return $response;
    }

    /**
     * @deprecated
     * @return array
     */
    public function cancelOrder($putData)
    {
        return [];
    }

    /**
     * @deprecated
     * @return array
     */
    public function getCurrentCurrencyRate()
    {
        return [];
    }

    public function setVatrefundForm($countryCode, $amount, $vatProductrate)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/vatrefundcal.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("from helper");

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $logger->info("Token: " . $access_token);
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization:" . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/vatRefundCalculator";

        $postData = json_encode([
            'country_code_iso3' => $countryCode,
            'amount' => $amount,
            'vat_product_rate' => $vatProductrate
        ]);

        $logger->info("Url: " . $call_url);
        $logger->info("Post Data: " . $postData);

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);
            $logger->info(print_r($response, true) . " response");
            if (curl_errno($ch)) {
                $logger->err('Curl error: ' . curl_error($ch));
            }
            curl_close($ch);

            $logger->info("Response: " . print_r($response, true));
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $logger->err('Exception: ' . $e->getMessage());
        }

        return $response;
    }


    public function countriesList(): array
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/countrieslist.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("from helper");

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;
        $call_url = $url . "companies/" . $company_id . "/ushop/retailers/countries/list";
        $logger->info("Url: " . $call_url);
        $response = null;
        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $raw_response = curl_exec($ch);

            if (curl_errno($ch)) {
                $logger->err('Curl error: ' . curl_error($ch));
            } else {
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $logger->info('HTTP Code: ' . $http_code);
                if ($http_code == 200) {
                    $response = json_decode($raw_response, true);
                    $logger->info("Response: " . print_r($response, true));
                } else {
                    $logger->err('HTTP error code: ' . $http_code . ' Response: ' . $raw_response);
                }
            }

            curl_close($ch);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $logger->err('Exception: ' . $e->getMessage());
        }

        return $response ? $response : [];
    }

    public function branchesByretailers()
    {
        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;
        $reailerBranches = [];
        $branches = $this->retailersByCountries();

        // print_r($branches);die;

        if ($branches['status'] === 'success' && isset($branches['data'])) {
            // Access the data array
            $data = $branches['data'];

            // Loop through the data array
            foreach ($data as $countryCode => $retailers) {
                foreach ($retailers as $retailer) {
                    // Access the 'id' key for each retailer

                    $id = $retailer['id'];
                    $country_code = $retailer['country_code'];
                    $call_url = $url . "companies/" . $company_id . "/comave/retailer/{$id}/branches";
                    try {
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $call_url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_ENCODING, '');
                        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                        curl_setopt($ch, CURLOPT_POST, false);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorization]);
                        $response = json_decode(curl_exec($ch), true);
                        // echo "response array: " . print_r($response, true);exit;
                        curl_close($ch);
                        if ($response && isset($response['status']) && $response['status'] === 'success') {
                            // Add branches for this country to the result array
                            $reailerBranches[$country_code] = $response['data'];
                        }
                    } catch (\Exception $e) {
                        // Handle exceptions if needed
                    }
                }
            }
        }

        return $reailerBranches;
    }

    public function retailersByCountries()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/retailersByCountries.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("from helper");

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/retailersByCountries?search=";

        $logger->info("Url: " . $call_url);

        $response = null;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $raw_response = curl_exec($ch);

            if (curl_errno($ch)) {
                $logger->err('Curl error: ' . curl_error($ch));
            } else {
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $logger->info('HTTP Code: ' . $http_code);
                if ($http_code == 200) {
                    $response = json_decode($raw_response, true);
                    $logger->info("Response: " . print_r($response, true));
                } else {
                    $logger->err('HTTP error code: ' . $http_code . ' Response: ' . $raw_response);
                }
            }

            curl_close($ch);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $logger->err('Exception: ' . $e->getMessage());
        }

        return $response;
    }

    public function countriesForCurrencies()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/currencies.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("from helper");

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $logger->info("Token: " . $access_token);
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/currencies";

        $logger->info("Url: " . $call_url);

        $response = null;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $raw_response = curl_exec($ch);

            if (curl_errno($ch)) {
                $logger->err('Curl error: ' . curl_error($ch));
            } else {
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $logger->info('HTTP Code: ' . $http_code);
                if ($http_code == 200) {
                    $response = json_decode($raw_response, true);
                    $logger->info("Response: " . print_r($response, true));
                } else {
                    $logger->err('HTTP error code: ' . $http_code . ' Response: ' . $raw_response);
                }
            }

            curl_close($ch);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $logger->err('Exception: ' . $e->getMessage());
        }

        return $response;
    }

    public function currencyRatesInDollar()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/currencyRatesInDollar.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("from helper");

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $logger->info("Token: " . $access_token);
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/currencyRatesInDollar";

        $logger->info("Url: " . $call_url);

        $response = null;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $raw_response = curl_exec($ch);

            if (curl_errno($ch)) {
                $logger->err('Curl error: ' . curl_error($ch));
            } else {
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $logger->info('HTTP Code: ' . $http_code);
                if ($http_code == 200) {
                    $response = json_decode($raw_response, true);
                    $logger->info("Response: " . print_r($response, true));
                } else {
                    $logger->err('HTTP error code: ' . $http_code . ' Response: ' . $raw_response);
                }
            }

            curl_close($ch);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $logger->err('Exception: ' . $e->getMessage());
        }

        return $response;
    }

    public function storeDetails($branchId)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/storeDetails.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("from helper");

        $url = $this->getUshopSiteUrl();
        $access_token = $this->getUshopAuth();
        $logger->info("Token: " . $access_token);
        $company_id = $this->getUshopCompId();
        $authorization = "Authorization: " . $access_token;

        $call_url = $url . "companies/" . $company_id . "/comave/branch/" . $branchId;

        $logger->info("Url: " . $call_url);

        $response = null;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $call_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $raw_response = curl_exec($ch);

            if (curl_errno($ch)) {
                $logger->err('Curl error: ' . curl_error($ch));
            } else {
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $logger->info('HTTP Code: ' . $http_code);
                if ($http_code == 200) {
                    $response = json_decode($raw_response, true);
                    $logger->info("Response: " . print_r($response, true));
                } else {
                    $logger->err('HTTP error code: ' . $http_code . ' Response: ' . $raw_response);
                }
            }

            curl_close($ch);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $logger->err('Exception: ' . $e->getMessage());
        }

        return $response;
    }
}
