<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Block;

/*
 * Webkul Marketplace Order View Block
 */
use Magento\Sales\Model\Order;
use Magento\Customer\Model\Customer;
use Webkul\Marketplace\Model\OrdersFactory as MpOrderModel;

class SellerData extends \Magento\Framework\View\Element\Template
{
    protected Customer $customer;
    protected Order $order;
    protected MpOrderModel $mpOrderModel;

    public function __construct(
        Order $order,
        Customer $customer,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Framework\View\Element\Template\Context $context,
        MpOrderModel $mpOrderModel,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $collectionFactory,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        array $data = []
    ) {
        $this->Order = $order;
        $this->Customer = $customer;
        $this->_customerSession = $customerSession;
        $this->mpOrderModel = $mpOrderModel;
        $this->collectionFactory = $collectionFactory;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        parent::__construct($context, $data);
    }

    public function getSellerOrderInfo($orderId, $sellerId)
    {
        $collection = $this->mpOrderModel->create()->getCollection()
        ->addFieldToFilter(
            'order_id',
            ['eq' => $orderId]
        )
        ->addFieldToFilter(
            'seller_id',
            ['eq' => $sellerId]
        );

        return $collection;
    }

    public function getCustomerInfo($uid,$websiteid)
    {
        $customerCollection = $this->collectionFactory->create();
        $customerCollection->addAttributeToSelect('*')
                           ->addAttributeToFilter('commave_uuid',$uid)
                           ->addAttributeToFilter('website_id', array('eq' => $websiteid))
                           ->load();

        return $customerCollection;
    }

    public function getCustomerId(): int
    {
        return (int)$this->_customerSession->getCustomer()->getId();
    }

    public function getMarkateplaceOrder(string $orderId)
    {
        return $this->mpOrderModel->create()->getCollection()
        ->addFieldToFilter(
            'order_id',
            ['eq' => $orderId]
        );
    }
}
