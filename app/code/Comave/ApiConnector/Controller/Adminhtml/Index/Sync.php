<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Controller\Adminhtml\Index;

use AllowDynamicProperties;
use Magento\Backend\App\Action;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Io\File;
use Webkul\Marketplace\Helper\Data as MarketplaceHelperData;
use Webkul\Marketplace\Model\Product as SellerProduct;
use Magento\Indexer\Model\IndexerFactory;
use Magento\Indexer\Model\Indexer\Collection;

#[AllowDynamicProperties]
class Sync extends Action
{
    /**
     * @var MarketplaceHelperData
     */
    protected $_marketplaceHelperData;

    /**
     * Directory List
     *
     * @var DirectoryList
     */
    protected $directoryList;

    /**
     * File interface
     *
     * @var File
     */
    protected $file;

    public function __construct(
    	\Magento\Backend\App\Action\Context $context,
    	\Comave\ApiConnector\Helper\Data $dataHelper,
    	\Magento\Framework\View\Result\PageFactory $resultPageFactory,
    	\Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
    	\Magento\Catalog\Api\Data\ProductInterfaceFactory $productFactory,
    	\Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry,
    	\Magento\Framework\Message\ManagerInterface $messageManager,
    	\Magento\Catalog\Model\Product $_product,
    	MarketplaceHelperData $marketplaceHelperData,
    	\Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
    	\Magento\Customer\Model\CustomerFactory $customerFactory,
    	DirectoryList $directoryList,
        IndexerFactory $indexFactory,
        Collection $indexCollection,
    	File $file
    ) {
    	$this->dataHelper = $dataHelper;
    	$this->resultPageFactory = $resultPageFactory;
    	$this->productRepository = $productRepository;
    	$this->productFactory = $productFactory;
    	$this->stockRegistry = $stockRegistry;
    	$this->_messageManager = $messageManager;
    	$this->product = $_product;
    	$this->_marketplaceHelperData = $marketplaceHelperData;
    	$this->_productCollectionFactory = $productCollectionFactory;
    	$this->_customerFactory = $customerFactory;
    	$this->directoryList = $directoryList;
        $this->indexCollection = $indexCollection;
        $this->indexFactory = $indexFactory;
    	$this->file = $file;

    	parent::__construct($context);
    }

    public function execute()
    {
        // @TODO: will need to be refactored
        $this->messageManager->addErrorMessage(__(" Seller Data sync module disabled "));
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        return $resultRedirect->setUrl($this->_redirect->getRefererUrl());

		$is_sync_wp = $this->dataHelper->getAvailable();
        if($is_sync_wp) {
			$vendorDetails = $this->dataHelper->getVendors();

			$vendorIds = 0;
			$wholeData = array();
			$prodData = array();
			$associatedProductIds = array();
			$editFlag = 0;
			$status1 = $this->_marketplaceHelperData->getIsProductApproval() ? SellerProduct::STATUS_DISABLED : SellerProduct::STATUS_ENABLED;

			if($vendorDetails){
				foreach ($vendorDetails['data'] as $key => $value) {
					$synced = 0;
					$updated = 0;
					$vendorIds = $value['commave_uuid'];
					$comapany_name = $value['company_name'];
					$response = $this->dataHelper->getCustProduct($vendorIds);

					$custCollection = $this->_customerFactory->create()->getCollection()
					->addAttributeToSelect("*")
					->addAttributeToFilter("commave_uuid", $vendorIds)
					->load();

					if($custCollection->count() >= 1){
						foreach($custCollection as $customer){
							$sellerId = $customer->getId();
							$website = $customer->getWebsiteId();
							if(!empty($response['data'])){
								foreach($response['data']['Single_product'] as $value){
									$product = (array)$value;

									$price = $product['sales_price'];
									$desc = $product['item_desc'];
									$unitId = $product['Unit'][0]['unit_id'];
									$prodId = $product['id'];

									if(($website == 1 && $product['ComAve'] == 1) || ($website == 2 && $product['Venstation'] == 1)){

										if(empty($price)){
											$price = 0;
										}

										if($website == 1 && $product['ComAve'] == 1){
											$sku = $product['comAve_item_code'];
											$pname = $product['item_name']." [com-".$product['item_code']."]";
										}
										if($website == 2 && $product['Venstation'] == 1){
											$sku = $product['venstation_item_code'];
											$pname = $product['item_name']." [ven-".$product['item_code']."]";
										}

										$collection = $this->_productCollectionFactory->create()
										->addAttributeToSelect('*')
										->addAttributeToFilter("sku",$sku)->load();
										$pid = $collection->count();

										if($pid == 0){
											$prod = $this->productFactory->create();
											$prod->setSku($sku);
											$prod->setName($pname);

											$qty = $product['Available_stock'];
											if(empty($qty)){
												$qty = 0;
											}

											$cate = array();
											if($product['ComAve & Venstation']){
												foreach($product['ComAve & Venstation'] as $value){
													$info=(array)$value;
													$cate[] = $info['commaveCategoryId'];
												}
											}

											$websiteIds = array();
											if($website == 1 && $product['ComAve'] == 1){
												$websiteIds[] = 1;
											}
											if($website == 2 && $product['Venstation'] == 1){
												$websiteIds[] = 2;
											}

											$prodImg = array();
											if($product['Uploaded_images']){
												foreach($product['Uploaded_images'] as $value){
													$info=(array)$value;
													if($info['image'] != NULL){
														$prodImg[] = $info['image'];
													}
												}
											}

											$tmpDir = $this->getMediaDirTmpDir();
											/** create folder if it is not exists */
											$this->file->checkAndCreateFolder($tmpDir);

											foreach($prodImg as $imageUrl){
												/** @var string $newFileName */
												$newFileName = $tmpDir . baseName($imageUrl);

												/** read file from URL and copy it to the new destination */
												$result = $this->file->read($imageUrl, $newFileName);
												if ($result) {
													$prod->addImageToMediaGallery($newFileName, array('image', 'small_image', 'thumbnail','swatch_image'), false, false);
												}
											}

											$prod->setVisibility(4);
											$prod->setPrice($price);
											$prod->setStockData(['qty' => $qty, 'is_in_stock' => 1]);
											$prod->setQuantityAndStockStatus(['qty' => $qty, 'is_in_stock' => 1]);
											$prod->setTypeId(\Magento\Catalog\Model\Product\Type::TYPE_SIMPLE);
											$prod->setAttributeSetId(4);
											$prod->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED);
											$prod->setWebsiteIds($websiteIds);
											$prod->setCategoryIds($cate);
											$prod->setDescription($desc);


											try {
												$prod->save();
												$synced++;

												$_prod = $this->productRepository->getById($prod->getId());
												$_prod->setData('unit_id', $unitId);
												$_prod->setData('prod_id', $prodId);
												$this->productRepository->save($_prod);

												$this->dataHelper->saveSellerProducts(
													$prod->getId(),
													$prod->getRowId(),
													$sellerId,
													$status1,
													$editFlag,
													$associatedProductIds
												);

												$indexes = $this->indexCollection->getAllIds();
												foreach ($indexes as $index){
													$indexFactory = $this->indexFactory->create()->load($index);
													$indexFactory->reindexAll($index);
													$indexFactory->reindexRow($index);
												}

											} catch (Exception $e) {
												$this->messageManager->addError($e, __('We can\'t save the product data.'));
											}
										}else{

											$_product = $this->product->loadByAttribute('sku', $sku);

											$qty = $product['Available_stock'];
											if(empty($qty)){
												$qty = 0;
											}

											$cate = array();
											if($product['ComAve & Venstation']){
												foreach($product['ComAve & Venstation'] as $value){
													$info=(array)$value;
													$cate[] = $info['commaveCategoryId'];
												}
											}

											$websiteIds = array();
											if($website == 1 && $product['ComAve'] == 1){
												$websiteIds[] = 1;
											}
											if($website == 2 && $product['Venstation'] == 1){
												$websiteIds[] = 2;
											}

											try {
												$_product->setName($pname)
												->setStockData(['qty' => $qty, 'is_in_stock' => 1])
												->setQuantityAndStockStatus(['qty' => $qty, 'is_in_stock' => 1])
												->setVisibility(4)
												->setPrice($price)
												->setCategoryIds($cate)
												->setWebsiteIds($websiteIds)
												->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED)
												->setStoreId(0)
												->save();

												$updated++;
											}catch (Exception $e) {
												$this->messageManager->addError($e, __('We can\'t Update the product data.'));
											}
										}
									}elseif(($website == 1) && ($product['ComAve'] == 0) && ($product['comAve_item_code'] != NULL)) {
										$_product1 = $this->product->loadByAttribute('sku', $product['comAve_item_code']);

										try{
											if($_product1){
												$_product1->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED)->setStoreId(0)->save();
											}
										}catch (Exception $e) {
										$this->messageManager->addError($e, __('We can\'t Update the product data.'));
									}
									}elseif($website == 2 && $product['Venstation'] == 0 && $product['venstation_item_code'] != NULL){
										$_product2 = $this->product->loadByAttribute('sku', $product['venstation_item_code']);

									try {
											if($_product2){
												$_product2->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED)->setStoreId(0)->save();
											}
										}catch (Exception $e) {
											$this->messageManager->addError($e, __('We can\'t Update the product data.'));
										}
									}
								}

								$indexes = $this->indexCollection->getAllIds();
								foreach ($indexes as $index){
									$indexFactory = $this->indexFactory->create()->load($index);
									$indexFactory->reindexAll($index);
									$indexFactory->reindexRow($index);
								}

							}else{
								$this->messageManager->addErrorMessage(__(" Unable to fetch information "));
								continue;
							}
							if ($synced) {
								$this->messageManager->addSuccessMessage(__('%1 product(s) in sync for %2', $synced,$comapany_name));
							}elseif ($updated) {
								$this->messageManager->addSuccessMessage(__('%1 product(s) Updated for %2', $updated,$comapany_name));
							}
						}
					}
				}
			}
			$resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
			return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
		}else{
			$this->messageManager->addErrorMessage(__(" Seller Data sync module disabled "));
			$resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
			return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
		}
    }

 	/**
     * Media directory name for the temporary file storage
     * pub/media/images
     *
     * @return string
     */
 	protected function getMediaDirTmpDir()
 	{
         return $this->directoryList->getPath(DirectoryList::MEDIA) . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR;
 	}
 }
