<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Controller\Adminhtml\Index;

use AllowDynamicProperties;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Io\File;
use Webkul\Marketplace\Helper\Data as MarketplaceHelperData;
use Webkul\Marketplace\Model\Product as SellerProduct;
use Magento\Indexer\Model\IndexerFactory;
use Magento\Indexer\Model\Indexer\Collection;

#[AllowDynamicProperties]
class ProdSync extends \Magento\Backend\App\Action
{
    /**
     * @var MarketplaceHelperData
     */
    protected $_marketplaceHelperData;

    /**
    *  @var \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
    */
    protected $_customerRepositoryInterface;

    /**
     * Directory List
     *
     * @var DirectoryList
     */
    protected $directoryList;

    /**
     * File interface
     *
     * @var File
     */
    protected $file;

    /**
     * Execute view action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    /**
     * Constructor
     *
     * @param \Magento\Backend\Block\Widget\Context $context
     * @param \Magento\Framework\Registry $registry
     *  @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
     * @param AccountManagementInterface $customerAccountManagement
     */

    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        \Comave\ApiConnector\Helper\Data $dataHelper,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Magento\Catalog\Api\Data\ProductInterfaceFactory $productFactory,
        \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Catalog\Model\Product $_product,
        MarketplaceHelperData $marketplaceHelperData,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollection,
        \Magento\Catalog\Model\Product\Gallery\ReadHandler $galleryReadHandler,
        \Magento\Catalog\Model\Product\Gallery\Processor $imageProcessor,
        \Magento\Catalog\Model\ResourceModel\Product\Gallery $productGallery,
        DirectoryList $directoryList,
        IndexerFactory $indexFactory,
        Collection $indexCollection,
        File $file
    ) {
        parent::__construct($context);

        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->dataHelper = $dataHelper;
        $this->resultPageFactory = $resultPageFactory;
        $this->productRepository = $productRepository;
        $this->productFactory = $productFactory;
        $this->stockRegistry = $stockRegistry;
        $this->_messageManager = $messageManager;
        $this->product = $_product;
        $this->_marketplaceHelperData = $marketplaceHelperData;
        $this->_productCollectionFactory = $productCollectionFactory;
        $this->_customerFactory = $customerFactory;
        $this->_categoryCollection = $categoryCollection;
        $this->galleryReadHandler = $galleryReadHandler;
        $this->imageProcessor = $imageProcessor;
        $this->productGallery = $productGallery;
        $this->directoryList = $directoryList;
        $this->indexCollection = $indexCollection;
        $this->indexFactory = $indexFactory;
        $this->file = $file;
    }

    public function execute()
    {
        // @TODO: will need to be refactored
        $this->messageManager->addErrorMessage(__(" Seller Product Data sync is disabled "));
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        return $resultRedirect->setUrl($this->_redirect->getRefererUrl());

        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/upsync.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("My message");

        $is_sync_wp = $this->dataHelper->getAvailable();

        if($is_sync_wp){

            $data = $this->getRequest()->getParams();

            $custId = $data['customer_id'];
            $customer = $this->_customerRepositoryInterface->getById($custId);
            $selerId = $custId;

            $comaveUid =  $customer->getCustomAttribute('commave_uuid');
            $uidData = (array) $comaveUid;

            foreach ($uidData as $key => $value) {
                $uuid  = $value['value'];
            }
            $response = $this->dataHelper->getCustProduct($uuid);

            $synced = 0;
            $updated = 0;
            $editFlag = 0;
            $associatedProductIds = array();
            $itemStatus = array();

            $website = $customer->getWebsiteId();

            $status1 = $this->_marketplaceHelperData->getIsProductApproval() ? SellerProduct::STATUS_DISABLED : SellerProduct::STATUS_ENABLED;

            foreach($response['data']['Single_product'] as $value){

                $product = (array)$value;

                $price = $product['sales_price'];
                $special_price = number_format($product['special_price'], 2);
                $desc = $product['item_desc'];
                $unitId = $product['Unit'][0]['unit_id'];
                $prodId = $product['id'];
                if(($website == 1 && $product['ComAve'] == 1) || ($website == 2 && $product['Venstation'] == 1)){

                    if(empty($price)){
                        $price = 0;
                    }
                    $pname = $product['item_name'];

                    if($website == 1 && $product['ComAve'] == 1){
                        $sku = $product['comAve_item_code'];
                        $urlName = $product['item_name']." [com-".$product['item_code']."]";
                        $url = preg_replace('#[^0-9a-z]+#i', '-', $urlName);
                        $url = strtolower($url);
                    }
                    if($website == 2 && $product['Venstation'] == 1){
                        $sku = $product['venstation_item_code'];
                        $urlName = $product['item_name']." [ven-".$product['item_code']."]";
                        $url = preg_replace('#[^0-9a-z]+#i', '-', $urlName);
                        $url = strtolower($url);
                    }

                    $collection = $this->_productCollectionFactory->create()
                            ->addAttributeToSelect('*')
                            ->addAttributeToFilter("sku",$sku)->load();

                    $pid = $collection->count();

                    if($pid == 0){
                        $prod = $this->productFactory->create();
                        $prod->setSku($sku);
                        $prod->setName($pname);

                        $qty = $product['Available_stock'];
                        if(empty($qty)){
                            $qty = 0;
                        }

                        $cate = array();
                        if($product['ComAve & Venstation']){
                            foreach($product['ComAve & Venstation'] as $value){
                                $info=(array)$value;
                                $categoryTitle = end($info['category_name']);

                                $catCollection = $this->_categoryCollection->create()
                                                ->addAttributeToFilter('name',$categoryTitle)
                                                ->setPageSize(1);

                                if ($catCollection->getSize()) {
                                    $cate[] = $catCollection->getFirstItem()->getId();
                                }
                            }
                        }

                        $websiteIds = array();
                        if($website == 1 && $product['ComAve'] == 1){
                            $websiteIds[] = 1;
                        }
                        if($website == 2 && $product['Venstation'] == 1){
                            $websiteIds[] = 2;
                        }

                        $prodImg = array();
                        if($product['Uploaded_images']){
                            foreach($product['Uploaded_images'] as $value){
                                $info=(array)$value;
                                if($info['image'] != NULL){
                                    $prodImg[] = $info['image'];
                                }
                            }
                        }

                        $tmpDir = $this->getMediaDirTmpDir();
                        /** create folder if it is not exists */
                        $this->file->checkAndCreateFolder($tmpDir);

                        foreach($prodImg as $imageUrl){
                            /** @var string $newFileName */
                            $newFileName = $tmpDir . baseName($imageUrl);

                            /** read file from URL and copy it to the new destination */
                            $result = $this->file->read($imageUrl, $newFileName);
                            if ($result) {
                                $prod->addImageToMediaGallery($newFileName, array('image', 'small_image', 'thumbnail','swatch_image'), false, false);
                            }
                        }

                        $prod->setVisibility(4);
                        $prod->setPrice($price);
                        $prod->setUrlKey($url);
                        $prod->setStockData(['qty' => $qty, 'is_in_stock' => 1]);
                        $prod->setQuantityAndStockStatus(['qty' => $qty, 'is_in_stock' => 1]);
                        $prod->setTypeId(\Magento\Catalog\Model\Product\Type::TYPE_SIMPLE);
                        $prod->setVisibility(4);
                        $prod->setAttributeSetId(4);
                        $prod->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED);
                        $prod->setWebsiteIds($websiteIds);
                        $prod->setCategoryIds($cate);
                        $prod->setDescription($desc);

                        try {
                            $prod->save();
                            $synced++;

                            $_prod = $this->productRepository->getById($prod->getId());
                            $_prod->setData('unit_id', $unitId);
                            $_prod->setData('prod_id', $prodId);

                            if(($special_price > 0) && ($website == 1)){
                                $_prod->setData('special_price', $special_price);
                            }

                            $this->productRepository->save($_prod);

                            $tierPrices = array();
                            $tierValue = $product['Venstation_Pricing'][0]['quantity'];
                            if($website == 2 && !is_null($tierValue)){
                                foreach ($product['Venstation_Pricing'] as $tierPricing) {
                                    $ruleQty = '';
                                    $customerGroupId = 32000;

                                    if($tierPricing['price_type'] == "FIXED AMOUNT"){
                                        $ruleQty = $tierPricing['quantity'];
                                        $priceValue = number_format($tierPricing['price_value'], 2);

                                        $tierPrices[] = [
                                            'website_id' => 0,
                                            'cust_group' => $customerGroupId,
                                            'website_price' => $priceValue,
                                            'price' => $priceValue,
                                            'all_groups' => 1,
                                            'price_qty' => $ruleQty
                                        ];
                                    }elseif($tierPricing['price_type'] == "DISCOUNT (%)"){
                                        $ruleQty = $tierPricing['quantity'];
                                        $priceValue = number_format($tierPricing['price_value'], 2);

                                        $tierPrices[] = [
                                            'website_id' => 0,
                                            'cust_group' => $customerGroupId,
                                            'website_price' => $priceValue,
                                            'price' => 0,
                                            'all_groups' => 1,
                                            'price_qty' => $ruleQty,
                                            'percentage_value'  => $priceValue
                                        ];
                                    }
                                }

                                $_product = $this->product->load($prod->getId());
                                $_product->setData('tier_price', $tierPrices);
                                $_product->save();
                            }

                            $itemStatus[] = $sku;

                            $this->dataHelper->saveSellerProducts(
                                $prod->getId(),
                                $prod->getRowId(),
                                $selerId,
                                $status1,
                                $editFlag,
                                $associatedProductIds
                            );

                            $indexes = $this->indexCollection->getAllIds();
                            foreach ($indexes as $index){
                                $indexFactory = $this->indexFactory->create()->load($index);
                                $indexFactory->reindexAll($index);
                                $indexFactory->reindexRow($index);
                            }
                        }
                        catch (Exception $e) {
                            $this->messageManager->addError($e, __('We can\'t save the product data.'));
                        }

                    }else{

                        $_product = $this->product->loadByAttribute('sku', $sku);

                        $qty = $product['Available_stock'];
                        if(empty($qty)){
                            $qty = 0;
                        }

                        $cate = array();

                        if($product['ComAve & Venstation']){
                            foreach($product['ComAve & Venstation'] as $value){
                                $info=(array)$value;
                                $categoryTitle = end($info['category_name']);

                                $catCollection = $this->_categoryCollection->create()
                                                ->addAttributeToFilter('name',$categoryTitle)
                                                ->setPageSize(1);

                                if ($catCollection->getSize()) {
                                    $cate[] = $catCollection->getFirstItem()->getId();
                                }
                            }
                        }

                        $indexes = $this->indexCollection->getAllIds();
                        foreach ($indexes as $index){
                            $indexFactory = $this->indexFactory->create()->load($index);
                            $indexFactory->reindexAll($index);
                            $indexFactory->reindexRow($index);
                        }

                        $this->galleryReadHandler->execute($_product);
                        $images = $_product->getMediaGalleryImages();

                        $apiImg = array();
                        foreach($images as $key => $child) {
                            $apiImg[] = baseName($child->getFile());
                        }

                        $prodImg = array();
                        if($product['Uploaded_images']){
                            foreach($product['Uploaded_images'] as $value){
                                $info=(array)$value;
                                if($info['image'] != NULL){
                                    $prodImg[] = $info['image'];
                                }
                            }
                        }

                        $prodImgBase = array();
                        foreach($prodImg as $child) {
                            $prodImgBase[] = baseName($child);
                        }

                        $addImg = array_diff($prodImgBase,$apiImg);
                        $delImg = array_diff($apiImg,$prodImgBase);

                        //code for add images
                        $tmpDir = $this->getMediaDirTmpDir();
                        $this->file->checkAndCreateFolder($tmpDir);

                        if(!empty($addImg)){
                            foreach($prodImg as $imageUrl){
                                $pimg = baseName($imageUrl);

                                foreach ($addImg as $value) {
                                    if ($pimg == $value) {
                                        $newFileName = $tmpDir . baseName($imageUrl);
                                        $result = $this->file->read($imageUrl, $newFileName);

                                        if ($result) {
                                            $_product->addImageToMediaGallery($newFileName, array('image', 'small_image', 'thumbnail','swatch_image'), false, false);
                                            $_product->save();
                                        }
                                    }
                                }
                            }
                        }

                        //code for delete images
                        if(!empty($delImg)){
                            foreach($images as $child) {
                                $childBase = baseName($child->getFile());

                                foreach ($delImg as $value) {
                                    if ($childBase == $value){
                                        $this->productGallery->deleteGallery($child->getValueId());
                                        $this->imageProcessor->removeImage($_product, $child->getFile());
                                        $_product->save();
                                    }
                                }
                            }
                        }

                        $websiteIds = array();
                        if($website == 1 && $product['ComAve'] == 1){
                            $websiteIds[] = 1;
                        }
                        if($website == 2 && $product['Venstation'] == 1){
                            $websiteIds[] = 2;
                        }

                        try {
                            $_product->setName($pname)
                                    ->setStockData(['qty' => $qty, 'is_in_stock' => 1])
                                    ->setQuantityAndStockStatus(['qty' => $qty, 'is_in_stock' => 1])
                                    ->setVisibility(4)
                                    ->setPrice($price)
                                    ->setCategoryIds($cate)
                                    ->setWebsiteIds($websiteIds)
                                    ->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED)
                                    ->setStoreId(0)
                                    ->save();

                            $_prod = $this->productRepository->getById($_product->getId());

                            if(($special_price > 0) && ($website == 1)){
                                $_prod->setData('special_price', $special_price);
                            }

                            $this->productRepository->save($_prod);

                            $tierPrices = array();
                            $tierValue = $product['Venstation_Pricing'][0]['quantity'];
                            if($website == 2 && !is_null($tierValue)){
                                foreach ($product['Venstation_Pricing'] as $tierPricing) {
                                    $ruleQty = '';
                                    $customerGroupId = 32000;

                                    if($tierPricing['price_type'] == "FIXED AMOUNT"){
                                        $ruleQty = $tierPricing['quantity'];
                                        $priceValue = number_format($tierPricing['price_value'], 2);

                                        $tierPrices[] = [
                                            'website_id' => 0,
                                            'cust_group' => $customerGroupId,
                                            'website_price' => $priceValue,
                                            'price' => $priceValue,
                                            'all_groups' => 1,
                                            'price_qty' => $ruleQty
                                        ];
                                    }elseif($tierPricing['price_type'] == "DISCOUNT (%)"){
                                        $ruleQty = $tierPricing['quantity'];
                                        $priceValue = number_format($tierPricing['price_value'], 2);

                                        $tierPrices[] = [
                                            'website_id' => 0,
                                            'cust_group' => $customerGroupId,
                                            'website_price' => $priceValue,
                                            'price' => 0,
                                            'all_groups' => 1,
                                            'price_qty' => $ruleQty,
                                            'percentage_value'  => $priceValue
                                        ];
                                    }
                                }

                                $_Product = $this->product->load($_product->getId());
                                $_Product->setData('tier_price', $tierPrices);
                                $_Product->save();
                            }

                            $updated++;
                            $itemStatus[] = $sku;

                        }catch (Exception $e) {
                            $this->messageManager->addError($e, __('We can\'t Update the product data.'));
                        }
                    }
                }elseif(($website == 1) && ($product['ComAve'] == 0) && ($product['comAve_item_code'] != NULL)){

                    $_product1 = $this->product->loadByAttribute('sku', $product['comAve_item_code']);

                    try {
                        if($_product1){
                            $_product1->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED)->setStoreId(0)->save();
                        }

                    }catch (Exception $e) {
                        $this->messageManager->addError($e, __('We can\'t Update the product data.'));
                    }
                }elseif(($website == 2) && ($product['Venstation'] == 0) && ($product['venstation_item_code'] != NULL)){

                    $_product2 = $this->product->loadByAttribute('sku', $product['venstation_item_code']);

                    try {
                        if($_product2){
                        $_product2->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED)->setStoreId(0)->save();
                        }
                    }catch (Exception $e) {
                        $this->messageManager->addError($e, __('We can\'t Update the product data.'));
                    }
                }
            }

            $indexes = $this->indexCollection->getAllIds();
            foreach ($indexes as $index){
                $indexFactory = $this->indexFactory->create()->load($index);
                $indexFactory->reindexAll($index);
                $indexFactory->reindexRow($index);
            }

            $comUid = array("itemCodes" => $itemStatus);
            $putData = json_encode($comUid);


            $responseStatus = $this->dataHelper->notSyncProductStatus($putData, $uuid);

            if ($synced) {
                $this->messageManager->addSuccessMessage(__('%1 product(s) in sync', $synced));
            }elseif ($updated) {
                $this->messageManager->addSuccessMessage(__('%1 product(s) Updated', $updated));
            }
            $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
            return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
        }else{
            $this->messageManager->addErrorMessage(__(" Seller Data sync module disabled "));
            $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
            return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
        }
    }


    /**
     * Media directory name for the temporary file storage
     * pub/media/images
     *
     * @return string
     */
    protected function getMediaDirTmpDir()
    {
        return $this->directoryList->getPath(DirectoryList::MEDIA) . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR;
    }
}
