<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\ApiConnector\Controller\Adminhtml\Index;


use AllowDynamicProperties;
use Magento\Framework\Event\ObserverInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;
use Magento\Framework\Controller\ResultFactory;
use Magento\Customer\Model\AddressFactory;
use Magento\Store\Model\StoreManagerInterface;
use Webkul\Marketplace\Model\SellerFactory as MpSeller;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Webkul\Marketplace\Helper\Email as MpEmailHelper;
use Magento\Customer\Model\AccountManagement;
use Magento\Store\Model\ResourceModel\Website\CollectionFactory as WebsiteCollectionFactory;
use Webkul\Marketplace\Model\SaleperpartnerFactory as MpSalesPartner;


#[AllowDynamicProperties]
class CustSync extends \Magento\Backend\App\Action
{
    protected $resultPageFactory;

    protected $_country;

    /**
    * @var CollectionFactory
    */
    protected $_collectionFactory;

    /**
    * @var MpSeller
    */
    protected $mpSeller;

    /**
    * @var MpHelper
    */
    protected $mpHelper;

    /**
    * @var MpEmailHelper
    */
    protected $mpEmailHelper;

     /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
     private $_messageManager;

    /**
    * @var \Magento\Framework\Stdlib\DateTime\DateTime
    */
    protected $_date;

    /**
    * @var \Magento\Customer\Model\CustomerFactory
    */
    protected $customerFactory;

    /**
    * @var \Magento\Customer\Model\AddressFactory
    */
    protected $addressFactory;

    /**
    * @var \Magento\Store\Model\StoreManagerInterface
    */
    protected $storeManager;

    protected $_customerAccountManagement;

    protected $customerResFactory;

    protected $websiteCollectionFactory;

    /**
    * Constructor
    *
    * @param \Magento\Backend\App\Action\Context  $context
    * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
    * @param MpSeller                                         $mpSeller
    * @param MpHelper                                         $mpHelper
    * @param \Magento\Framework\Message\ManagerInterface      $messageManager
    * @param MpEmailHelper                                    $mpEmailHelper
    * @param \Magento\Customer\Model\CustomerFactory $customerFactory
    * @param AddressFactory $addressFactory
    * @param StoreManagerInterface $storeManager
    * @param \Magento\Framework\Stdlib\DateTime\DateTime      $date
    */
    public function __construct(
        CollectionFactory $collectionFactory,
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Customer\Model\ResourceModel\CustomerFactory $customerResFactory,
        \Magento\Customer\Model\Customer $customerData,
        \Comave\ApiConnector\Helper\Data $dataHelper,
        AddressFactory $addressFactory,
        StoreManagerInterface $storeManager,
        \Magento\Directory\Model\Country $country,
        MpSeller $mpSeller = null,
        MpHelper $mpHelper = null,
        MpEmailHelper $mpEmailHelper = null,
        WebsiteCollectionFactory $websiteCollectionFactory,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Magento\Customer\Api\AccountManagementInterface $customerAccountManagement,
        MpSalesPartner $mpSalesPartner = null,
        \Webkul\MpVendorAttributeManager\Model\VendorGroupFactory $vendorGroupFactory
    ) {
        $this->_collectionFactory = $collectionFactory;
        $this->resultPageFactory = $resultPageFactory;
        $this->customerFactory = $customerFactory;
        $this->customerResFactory = $customerResFactory;
        $this->customer = $customerData;
        $this->dataHelper = $dataHelper;
        $this->addressFactory = $addressFactory;
        $this->storeManager = $storeManager;
        $this->_country = $country;
        $this->mpSeller = $mpSeller ?: \Magento\Framework\App\ObjectManager::getInstance()
        ->create(MpSeller::class);
        $this->mpHelper = $mpHelper ?: \Magento\Framework\App\ObjectManager::getInstance()
        ->create(MpHelper::class);
        $this->mpEmailHelper = $mpEmailHelper ?: \Magento\Framework\App\ObjectManager::getInstance()
        ->create(MpEmailHelper::class);
        $this->websiteCollectionFactory = $websiteCollectionFactory;
        $this->_messageManager = $messageManager;
        $this->_date = $date;
        $this->_customerAccountManagement = $customerAccountManagement;
        $this->mpSalesPartner = $mpSalesPartner ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(MpSalesPartner::class);
        $this->vendorGroupFactory = $vendorGroupFactory;
        parent::__construct($context);
    }
    /**
    * Execute view action
    *
    * @return \Magento\Framework\Controller\ResultInterface
    */
    public function execute()
    {
        // @TODO: will need to be refactored
        $this->messageManager->addErrorMessage(__(" Seller Customer Data sync is disabled "));
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        return $resultRedirect->setUrl($this->_redirect->getRefererUrl());

        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/synctest.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("My message");

        $is_sync_wp = $this->dataHelper->getAvailable();

        if($is_sync_wp){

            $vendorDetails = $this->dataHelper->getVendors();
            $synced = 0;
            $venGroupId = 0;

            $vendorGroupCollection = $this->vendorGroupFactory->create()->getCollection();
            $collectionSize = $vendorGroupCollection->getSize();

            if ($collectionSize > 0) {
                foreach($vendorGroupCollection as $venGroup){
                    if($venGroup->getGroupName() == "Retail Seller"){
                        $venGroupId = $venGroup->getEntityId();
                    }
                }
            }

            $allWebsiteIds = array();
            $websiteCollection = $this->websiteCollectionFactory->create();
            foreach($websiteCollection as $site){
                $allWebsiteIds[] = $site->getWebsiteId();
            }

            if($vendorDetails){
                foreach ($vendorDetails['data'] as $key => $val) {
                    $vendorIds = $val['commave_uuid'];
                    $comave_commission = $val['comave_commission'];
                    $venstation_commission = $val['venstation_commission'];
                    $custData = $this->dataHelper->getCustomer($vendorIds);

                    if((!empty($custData['data'])) && ($custData['data']['status'] == "ACTIVE")){
                        $custTypeId = $custData['data'][0]['id'];
                        $name = $custData['data'][0]['commercial_name'];

                        if($name == NULL){
                            $name = "test";
                        }
                        // removed the specila character from the name
                        $newCustName = preg_replace('/[@\.\;\" "\_\+\()\&]+/', ' ', $name);


                        $result = explode(" ", $newCustName, 2);
                        if(count($result) == 1){
                            $lname = "co";
                        }else{
                            $lname = $result[1];
                        }

                        $countryName = $custData['data']['country'];
                        $custAddress = $custData['data'][0]['address'];
                        $newCustAddress = preg_replace('/\n/', ' ', $custAddress);
                        $zipCode = $custData['data'][0]['zip_code'];
                        $city = 'ABC';
                        $postCode = '12345';
                        $email = $custData['data'][0]['email'];

                        // match api courntry code with magento controy code and mapped value to array
                        $countryCollection = $this->_country->getCollection();
                        foreach ($countryCollection as $country) {
                            $cname = $country->getName();
                            if(!empty($cname)){
                                similar_text($countryName,$cname,$percent);
                                if ($percent >= 85) {
                                    $countryId = $country->getCountryId();
                                    break;
                                }
                            }
                        }

                        // get website id from api for check uncheck set
                        $websiteId = array();
                        if($custData['data'][0]['ComAve'] == 1){
                            $websiteId[]  = $this->storeManager->getDefaultStoreView()->getWebsiteId();
                        }
                        if($custData['data'][0]['Venstation'] == 1){
                            $websiteId[] = 2;
                        }

                        if(empty($websiteId)){
                            $this->_messageManager->addError(__('Please select website to sync'));

                        }

                        if(!empty($websiteId)){
                            // create seller account websiet wise start
                            foreach($websiteId as $value){
                                $customer = $this->customerFactory->create();
                                $customer->setWebsiteId($value);
                                $disable = 0;
                                if ($customer->loadByEmail($custData['data'][0]['email'],$value)->getId()) {

                                    if(empty($lname)){
                                        $lname = "co";
                                    }
                                    $customer->setWebsiteId($value);
                                    $customer->setFirstname($result[0]);
                                    $customer->setLastname($lname);

                                    $customerData = $customer->getDataModel();
                                    $customerData->setCustomAttribute('phone_no',$custData['data'][0]['phone1']);

                                    $customer->updateData($customerData);
                                    $customerResource = $this->customerResFactory->create();
                                    $customerResource->saveAttribute($customer, 'phone_no');

                                    try {
                                        $customer->save();
                                    } catch (Exception $e) {
                                        $this->messageManager->addException($e, __('We can\'t save the customer.'));
                                    }

                                    $billingAddressId = $customer->getDefaultBilling();

                                    $customerAddress = $this->addressFactory->create()->load($billingAddressId);
                                    //$customerAddress = $this->addressFactory->create();

                                    $customerAddress->setCustomerId($customer->getId())
                                    ->setFirstname($result[0])
                                    ->setLastname($lname)
                                    ->setCountryId($countryId)
                                    ->setPostcode($postCode)
                                    ->setCity($city)
                                    ->setTelephone($custData['data'][0]['phone1'])
                                    ->setStreet($newCustAddress)
                                    ->setIsDefaultBilling('1')
                                    ->setIsDefaultShipping('1');

                                    try {
                                        $customerAddress->save();  // save customer address
                                        $synced++;
                                    } catch (Exception $e) {
                                        $this->messageManager->addError($e, __('We can\'t save the customer address.'));
                                    }

                                    $this->_messageManager->addSuccessMessage(__('%1 record updated',$custData['data'][0]['commercial_name']));


                                }else{

                                    if(empty($lname)){
                                        $lname = "co";
                                    }
                                    $customer->setWebsiteId($value);
                                    $customer->setEmail($custData['data'][0]['email']);
                                    $customer->setFirstname($result[0]);
                                    $customer->setLastname($lname);
                                    $customer->setPhoneNo($custData['data'][0]['phone1']);

                                    try {
                                        $customer->save();
                                    } catch (Exception $e) {
                                        $this->messageManager->addException($e, __('We can\'t save the customer.'));
                                    }
                                    $customerAddress = $this->addressFactory->create();

                                    //to save uid for seller id newly created and set the address data below
                                    //$cust = $this->customer->load($currentCustId);
                                    $customer->setWebsiteId($value);
                                    $customerData = $customer->getDataModel();
                                    $customerData->setCustomAttribute('commave_uuid',$vendorIds);
                                    $customerData->setCustomAttribute('is_vendor_group',$venGroupId);
                                    $customer->updateData($customerData);
                                    $customerResource = $this->customerResFactory->create();
                                    $customerResource->saveAttribute($customer, 'commave_uuid');
                                    $customerResource->saveAttribute($customer,'is_vendor_group');

                                    $customerAddress->setCustomerId($customer->getId())
                                    ->setFirstname($result[0])
                                    ->setLastname($lname)
                                    ->setCountryId($countryId)
                                    ->setPostcode($postCode)
                                    ->setCity($city)
                                    ->setTelephone($custData['data'][0]['phone1'])
                                    ->setStreet($newCustAddress)
                                    ->setIsDefaultBilling('1')
                                    ->setIsDefaultShipping('1')
                                    ->setSaveInAddressBook('1');

                                    try {
                                        $customerAddress->save();  // save customer address
                                        $synced++;
                                    } catch (Exception $e) {
                                        $this->messageManager->addError($e, __('We can\'t save the customer address.'));
                                    }

                                    try {
                                        $this->_customerAccountManagement->initiatePasswordReset(
                                                $email,
                                                AccountManagement::EMAIL_RESET,
                                                $customer->getWebsiteId()
                                            );
                                        } catch (\Exception $exception) {
                                            $this->messageManager->addErrorMessage(__(" Unable to send reset email. "));
                                        }
                                }

                                $currentCustId = $customer->getId();
                                $shopUrl = preg_replace('/[@\.\;\" "]+/', '_', $name);
                                if($value == 1){
                                    $profileShopUrl = strtolower("comave-".$shopUrl);
                                }elseif($value == 2){
                                    $profileShopUrl = strtolower("vens-".$shopUrl);
                                }else{
                                    $profileShopUrl = strtolower($shopUrl);
                                }

                                $isSellerAdd = true;

                                if ($profileShopUrl != '') {
                                    $profileurlcount = $this->_collectionFactory->create();
                                    $profileurlcount->addFieldToFilter('shop_url', $profileShopUrl);
                                    $sellerProfileIds = [];
                                    $sellerProfileUrl = '';
                                    $collectionselect = $this->_collectionFactory->create();

                                    $collectionselect->addFieldToFilter('seller_id', $currentCustId);
                                    foreach ($collectionselect as $coll) {
                                        array_push($sellerProfileIds, $coll->getEntityId());
                                        $sellerProfileUrl = $coll->getShopUrl();
                                    }
                                    if ($profileurlcount->getSize() && ($profileShopUrl != $sellerProfileUrl)) {
                                        $this->_messageManager->addError(__('This Shop URL already Exists.'));
                                    } else {
                                        $sellerStatus = $isSellerAdd ? 1 : 0;
                                        if (!empty($sellerProfileIds)) {
                                            foreach ($sellerProfileIds as $sellerProfileId) {
                                                $collection = $this->mpSeller->create()->load($sellerProfileId);
                                                $collection->setIsSeller($sellerStatus);
                                                $collection->setShopUrl($profileShopUrl);
                                                $collection->setSellerId($currentCustId);
                                                $collection->setCreatedAt($this->_date->gmtDate());
                                                $collection->setUpdatedAt($this->_date->gmtDate());
                                                $collection->save();
                                            }
                                        } else {
                                            $sellerProfileId = 0;
                                            $collection = $this->mpSeller->create()->load($sellerProfileId);
                                            $collection->setIsSeller($sellerStatus);
                                            $collection->setShopUrl($profileShopUrl);
                                            $collection->setStoreId(0);
                                            $collection->setSellerId($currentCustId);
                                            $collection->setCreatedAt($this->_date->gmtDate());
                                            $collection->setUpdatedAt($this->_date->gmtDate());
                                            $collection->save();

                                            $helper = $this->mpHelper;
                                            $adminStoreEmail = $helper->getAdminEmailId();
                                            $adminEmail = $adminStoreEmail ? $adminStoreEmail :
                                            $helper->getDefaultTransEmailId();
                                            $adminUsername = $helper->getAdminName();

                                            $seller = $this->customerFactory->create()->load($currentCustId);
                                            $baseUrl = $this->storeManager->getStore()->getBaseUrl();
                                            $emailTempVariables['myvar1'] = $seller->getName();

                                            $senderInfo = [
                                                'name' => $adminUsername,
                                                'email' => $adminEmail,
                                            ];
                                            $receiverInfo = [
                                                'name' => $seller->getName(),
                                                'email' => $seller->getEmail(),
                                            ];
                                            $this->mpEmailHelper->sendSellerApproveMail(
                                                $emailTempVariables,
                                                $senderInfo,
                                                $receiverInfo
                                            );
                                        }

                                    }
                                }
                            }
                        }


                        foreach($allWebsiteIds as $val){
                            $cust = $this->customerFactory->create();
                            $cust->setWebsiteId($val);

                            if($cust->loadByEmail($custData['data'][0]['email'],$val)->getId()) {
                                $custWebsite = $cust->getWebsiteId();
                                if(($custWebsite == 1) && ($custData['data'][0]['ComAve'] == 1)){

                                    $status = \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED;
                                    $sellerStatus = \Webkul\Marketplace\Model\Seller::STATUS_ENABLED;

                                    $collection = $this->mpSeller->create()
                                        ->getCollection()
                                        ->addFieldToFilter('seller_id', $cust->getId());

                                    foreach ($collection as $item) {
                                        $item->setIsSeller($sellerStatus);
                                        $item->save();
                                    }

                                    $collectionselect = $this->mpSalesPartner->create()
                                        ->getCollection()
                                        ->addFieldToFilter(
                                            'seller_id',
                                            $cust->getId()
                                        );
                                    if ($collectionselect->getSize() == 1) {
                                        foreach ($collectionselect as $verifyrow) {
                                            $autoid = $verifyrow->getEntityId();
                                        }

                                        $collectionupdate = $this->mpSalesPartner->create()->load($autoid);
                                        if ($comave_commission == 0) {
                                            $comave_commission = $collectionupdate->getCommissionRate();
                                        }

                                        $collectionupdate->setCommissionRate($comave_commission);
                                        $collectionupdate->setCommissionStatus(1);
                                        $collectionupdate->save();
                                    } else {
                                        if ($comave_commission == 0) {
                                            $comave_commission = 0;
                                        }
                                        $collectioninsert = $this->mpSalesPartner->create();
                                        $collectioninsert->setSellerId($cust->getId());
                                        $collectioninsert->setCommissionRate($comave_commission);
                                        $collectioninsert->setCommissionStatus(1);
                                        $collectioninsert->save();
                                    }

                                    $this->dataHelper->updateSellerProd($cust->getId(),$status);
                                    $this->messageManager->addSuccess(__('Seller has been Approved.'));
                                }elseif(($custWebsite == 1) && is_null($custData['data'][0]['ComAve'])){

                                    $status = \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED;
                                    $sellerStatus = \Webkul\Marketplace\Model\Seller::STATUS_DISABLED;

                                    $collection = $this->mpSeller->create()
                                        ->getCollection()
                                        ->addFieldToFilter('seller_id', $cust->getId());

                                    foreach ($collection as $item) {
                                        $item->setIsSeller($sellerStatus);
                                        $item->save();
                                    }

                                    $this->dataHelper->updateSellerProd($cust->getId(),$status);
                                    $this->messageManager->addSuccess(__('Seller has been Denied.'));
                                }

                                if(($custWebsite == 2) && ($custData['data'][0]['Venstation'] == 1)){

                                    $status = \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED;
                                    $sellerStatus = \Webkul\Marketplace\Model\Seller::STATUS_ENABLED;

                                    $collection = $this->mpSeller->create()
                                        ->getCollection()
                                        ->addFieldToFilter('seller_id', $cust->getId());

                                    foreach ($collection as $item) {
                                        $item->setIsSeller($sellerStatus);
                                        $item->save();
                                    }

                                    $collectionselect = $this->mpSalesPartner->create()
                                        ->getCollection()
                                        ->addFieldToFilter(
                                            'seller_id',
                                            $cust->getId()
                                        );
                                    if ($collectionselect->getSize() == 1) {
                                        foreach ($collectionselect as $verifyrow) {
                                            $autoid = $verifyrow->getEntityId();
                                        }

                                        $collectionupdate = $this->mpSalesPartner->create()->load($autoid);
                                        if ($venstation_commission == 0) {
                                            $venstation_commission = $collectionupdate->getCommissionRate();
                                        }
                                        $collectionupdate->setCommissionRate($venstation_commission);
                                        $collectionupdate->setCommissionStatus(1);
                                        $collectionupdate->save();
                                    }else {
                                        if ($venstation_commission == 0) {
                                            $venstation_commission = 0;
                                        }
                                        $collectioninsert = $this->mpSalesPartner->create();
                                        $collectioninsert->setSellerId($cust->getId());
                                        $collectioninsert->setCommissionRate($venstation_commission);
                                        $collectioninsert->setCommissionStatus(1);
                                        $collectioninsert->save();
                                    }

                                    $this->dataHelper->updateSellerProd($cust->getId(),$status);
                                    $this->messageManager->addSuccess(__('Seller has been Approved.'));
                                }elseif(($custWebsite == 2) && is_null($custData['data'][0]['Venstation'])){

                                    $status = \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED;
                                    $sellerStatus = \Webkul\Marketplace\Model\Seller::STATUS_DISABLED;

                                    $collection = $this->mpSeller->create()
                                        ->getCollection()
                                        ->addFieldToFilter('seller_id', $cust->getId());

                                    foreach ($collection as $item) {
                                        $item->setIsSeller($sellerStatus);
                                        $item->save();
                                    }
                                    $this->dataHelper->updateSellerProd($cust->getId(),$status);
                                    $this->messageManager->addSuccess(__('Seller has been Denied.'));
                                }
                            }
                        }
                    }elseif((!empty($custData['data'])) && ($custData['data']['status'] == "INACTIVE")){

                        foreach($allWebsiteIds as $val){
                            $cust = $this->customerFactory->create();
                            $cust->setWebsiteId($val);

                            //if(!empty($custData['data'])){

                                if($cust->loadByEmail($custData['data'][0]['email'],$val)->getId()) {

                                    if(($val == 1) && is_null($custData['data'][0]['ComAve'])){
                                        $status = \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED;
                                        $sellerStatus = \Webkul\Marketplace\Model\Seller::STATUS_DISABLED;

                                        $collection = $this->mpSeller->create()
                                            ->getCollection()
                                            ->addFieldToFilter('seller_id', $cust->getId());

                                        foreach ($collection as $item) {
                                            $item->setIsSeller($sellerStatus);
                                            $item->save();
                                        }

                                        $this->dataHelper->updateSellerProd($cust->getId(),$status);
                                        $this->messageManager->addSuccess(__('Seller has been Denied.'));
                                    }

                                    if(($val == 2) && is_null($custData['data'][0]['Venstation'])){
                                        $status = \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED;
                                        $sellerStatus = \Webkul\Marketplace\Model\Seller::STATUS_DISABLED;

                                        $collection = $this->mpSeller->create()
                                            ->getCollection()
                                            ->addFieldToFilter('seller_id', $cust->getId());

                                        foreach ($collection as $item) {
                                            $item->setIsSeller($sellerStatus);
                                            $item->save();
                                        }
                                        $this->dataHelper->updateSellerProd($cust->getId(),$status);
                                        $this->messageManager->addSuccess(__('Seller has been Denied.'));
                                    }
                                }
                            //}
                        }
                    }
                }
                if ($synced) {
                    $this->messageManager->addSuccessMessage(__('seller(s) %1 sync Successfully', $synced));
                }
                $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
                return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
            }else{
                $this->messageManager->addErrorMessage(__(" Unable to fetch Seller data "));
                $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
                return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
            }
        }else{
            $this->messageManager->addErrorMessage(__(" Seller Data sync module disabled "));
            $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
            return $resultRedirect->setUrl($this->_redirect->getRefererUrl());
        }
    }
}
