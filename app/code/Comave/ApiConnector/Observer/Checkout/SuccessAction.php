<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\ApiConnector\Observer\Checkout;

use Magento\Framework\Encryption\EncryptorInterface;
use Webkul\Marketplace\Controller\Order;
use Magento\Sales\Model\Order\ShipmentFactory;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Customer\Model\Session as CustomerSession;
use AllowDynamicProperties;
use Webkul\Marketplace\Service\UserInfoService;

#[AllowDynamicProperties]
class SuccessAction implements \Magento\Framework\Event\ObserverInterface
{

     /**
     * @var \Webkul\Marketplace\Model\SaleslistFactory
     */
    protected $saleslistFactory;

    protected $_shipmentFactory;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    protected $customerModel;

    /**
    * @var \Magento\Framework\Encryption\EncryptorInterface
    */
    protected $encryptor;

    protected $orderRepository;
    private $timezone;
    protected $customerSession;
    private \Psr\Log\LoggerInterface $logger;

    public function __construct(
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Webkul\Marketplace\Model\SaleslistFactory $saleslistFactory,
        \Magento\Customer\Model\CustomerFactory $customerModel,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        EncryptorInterface $encryptor,
        \Webkul\Marketplace\Model\OrdersFactory $MpOrdersModel,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Magento\Framework\Registry $registry,
        \Magento\Sales\Model\Order\Email\Sender\InvoiceSender $invoiceSender,
        \Webkul\Marketplace\Helper\Orders $orderHelper,
        ShipmentFactory $shipmentFactory,
        \Magento\Store\Api\WebsiteRepositoryInterface $websiteRepository,
        CollectionFactory $orderCollectionFactory,
        \Comave\ApiConnector\Helper\Data $dataHelper,
        \Comave\LixApiConnector\Helper\Data $dataLixHelper,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Catalog\Model\Product\Attribute\Repository $attributeRepository,
        CustomerSession $customerSession,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Coditron\CustomShippingRate\Model\Carrier $shippingCarrier,
        \Psr\Log\LoggerInterface $logger,
        private readonly UserInfoService $userInfoService,
    ) {
        $this->orderRepository = $orderRepository;
        $this->productRepository = $productRepository;
        $this->saleslistFactory = $saleslistFactory;
        $this->customerModel = $customerModel;
        $this->timezone = $timezone;
        $this->_date = $date;
        $this->encryptor = $encryptor;
        $this->MpOrdersModel = $MpOrdersModel;
        $this->objectManager = $objectManager;
        $this->_coreRegistry = $registry;
        $this->_invoiceSender = $invoiceSender;
        $this->orderHelper = $orderHelper;
        $this->_shipmentFactory = $shipmentFactory;
        $this->websiteRepository = $websiteRepository;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->dataHelper = $dataHelper;
        $this->dataLixHelper = $dataLixHelper;
        $this->_messageManager = $messageManager;
        $this->attributeRepository = $attributeRepository;
        $this->customerSession = $customerSession;
        $this->countryFactory = $countryFactory;
        $this->shippingCarrier = $shippingCarrier;
        $this->logger = $logger;
    }
    /**
     * Execute observer
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(
        \Magento\Framework\Event\Observer $observer
    ) {
        $navId = "";
        $clubId = "";
        $custData = array();
        $salesData = array();
        $orderUpdate = array();
        $sellerData = array();
        $sendOrder = array();
        $orderComplete = array();
        $isCpture = 0;

        $order = $observer->getEvent()->getOrder();
        $orderId = $order->getId();
        $websiteId = $order->getStore()->getWebsiteId();

        $website = $this->websiteRepository->getById($websiteId);
        $websiteName = $website->getName();

        $order = $this->orderRepository->get($orderId);
        $customerEmail = $order->getCustomerEmail();

        $guestCustomer = $order->getCustomerIsGuest();

        if($order->getShippingData()){
            $sellerShipData = json_decode($order->getShippingData(), TRUE);
          }

        if(!$guestCustomer){
            $custId = $order->getCustomerId();
            $customerOrder = $this->orderCollectionFactory->create()->addFieldToFilter('customer_id', $custId);
            $countOrderData = $customerOrder->getData();
            $orderCnt = $customerOrder->count();

            $customer = $this->customerModel->create()->load($custId);
            $clubId = $customer->getData('customerclub');
        }

        $payment = $order->getPayment();
        $paymentMethodCode = $payment->getMethod();


        $method = $payment->getMethodInstance();
        $methodTitle = $method->getTitle();
        $methodTitle =  preg_replace('/[@\.\;\/]+/', '', $methodTitle);
        if($methodTitle == "Cash On Delivery"){
            $payment_status = "POD";
        }else{
            $payment_status = "PREPAID";
        }


        /* get shipping info */
        $shipping = $order->getShippingMethod();
        $shipCarrier = $order->getShippingDescription();
        $shippingAmt = $order->getBaseShippingAmount();
        if($shipping == "tablerate_bestway"){
            $method = "First Class";
        }else{
            $method = substr($shipping, strpos($shipping, "_") + 1);
        }

        $shippingAddress = $order->getShippingAddress();
        $shipStreet = $shippingAddress->getStreet();

        //To check shipping and billing address are same
        $excludeKeys = array('entity_id', 'customer_address_id', 'quote_address_id', 'region_id', 'customer_id', 'address_type');
        $oBillingAddress = $order->getBillingAddress()->getData();
        $oShippingAddress = $order->getShippingAddress()->getData();
        $oBillingAddressFiltered = array_diff_key($oBillingAddress, array_flip($excludeKeys));
        $oShippingAddressFiltered = array_diff_key($oShippingAddress, array_flip($excludeKeys));

        $addressDiff = array_diff($oBillingAddressFiltered, $oShippingAddressFiltered);
        $billingAddress = $order->getBillingAddress();

        $itemData = array();
        $total_qty = 0;
        $seller_id = array();
        $tam_item = array();
        $count = 0;
        $sellerOrderapproval = 0;
        $_orderitemes = $order->getAllVisibleItems();
        foreach ( $_orderitemes as $item) {
            $Data = array();
            $tam_item_data = array();

            $sku = $item->getSku();
            $pname = rtrim(str_replace($sku,"",$item->getName()));
            $prodid = $item->getProductId();

            $wpId = $item->getProduct()->getData('prod_id');
            $unitId = $item->getProduct()->getData('unit_id');
            $rowTotal = $item->getRowTotal();
            $rowTotalTax = $item->getRowTotalInclTax();

            $seller = $this->getUserInfo($prodid,$order);
            $sellerOrderapproval = $this->getUserInfoOrderApproval($prodid,$order);

            if(!empty($seller)){
                $sellerId = $seller['uid'];
                $seller_id[] = $seller['id'];

                $Data["base_price"] = (float)$item->getPrice();
                $Data["base_price_incl_tax"] = (float)$item->getPriceInclTax();
                $Data["base_row_total"] = (float)$rowTotal;
                $Data["base_row_total_incl_tax"] = (float)$rowTotalTax;
                $Data["base_tax_amount"] = (float)$item->getTaxAmount();
                $Data["name"] = $item->getName();
                $Data["parent_id"] = "";
                $Data["item_code"] = $sku;
                $Data["quantity"] = (int)$item->getQtyOrdered();
                $Data["unit_id"] = (int)$unitId;
                $Data["commave_uuid"] = $sellerId;
                $Data["item_id"] = (int)$wpId;

                $itemData[] = $Data;
            }

            $total_qty = $total_qty + $item->getQtyOrdered();
        } //exit;

        $isCustomer =  $this->dataHelper->getCustomersByEmailId($customerEmail);

        if(empty($isCustomer['data'])){
            $customer_id = '';
            $custAddr = $shipStreet[0];
            if (array_key_exists(1,$shipStreet)){
                $custAddr .= " ".$shipStreet[1];
            }

            $custData['first_name'] = $shippingAddress->getFirstname();
            $custData['last_name'] = $shippingAddress->getLastname();
            $custData['mobile_number'] = $shippingAddress->getTelephone();
            $custData['phone_number'] = $shippingAddress->getTelephone();
            $custData['address'] = $custAddr;
            $custData['country'] = $shippingAddress->getCountryId();
            $custData['state'] = $shippingAddress->getRegion();
            $custData['city'] = $shippingAddress->getCity();
            $custData['pin_code'] = $shippingAddress->getPostcode();
            $custData['email'] = $customerEmail;
            $custData['payment_type'] = "credit";


            $postCustData = json_encode($custData);

            $resp = $this->dataHelper->setCustomers($postCustData);

            if(!empty($resp)){
                if(array_key_exists("Status",$resp)){
                    if($resp['Status'] == "Success"){
                        $getCustomer = $this->dataHelper->getCustomersByEmailId($customerEmail);
                        $customer_id = $getCustomer['data'][0]['customer_id'];

                    }
                }
            }

        }else{
            if(array_key_exists("data",$isCustomer)){
                $customer_id = $isCustomer['data'][0]['customer_id'];

            }
        }

        $shipData = array();
        $billData = array();
        $created = $order->getCreatedAt();
        $created = $this->timezone->date(new \DateTime($created));
        $date = $created->format('Y-m-d H:i:s');


        // $tam_date = geoip_time_zone_by_country_and_region('GB','V2');
        // $tam_create = $this->timezone->date(new \DateTime($tam_date));
        // $bookdate = $tam_create->format('Y-m-d H:i');

        $shipAddr = $shipStreet[0];
        if (array_key_exists(1,$shipStreet)){
            $shipAddr .= " ".$shipStreet[1];
        }

        $salesData['order_currency_code'] = $order->getOrderCurrencyCode();
        $salesData['base_discount_amount'] = (float)$order->getDiscountAmount();
        $salesData['base_grand_total'] = (float)$order->getGrandTotal();
        $salesData['base_shipping_amount'] = (float)$order->getShippingAmount();
        $salesData['base_subtotal'] = (float)$order->getSubtotal();
        $salesData['base_tax_amount'] = (float)$order->getShippingAddress()->getBaseTaxAmount();
        $salesData['total_qty'] = $total_qty;
        $salesData['issue_date'] = $date;
        $salesData['order_id'] = $order->getIncrementId();
        $salesData['website'] = $websiteName;
        $salesData['order_status'] = $order->getStatus();
        if($customer_id){
            $salesData['customer_id'] = (int)$customer_id;
        }
        $salesData['payment_status'] = $payment_status;

        $countryCode = $shippingAddress->getCountryId();
        $country = $this->countryFactory->create()->load($countryCode);
        $threeDigitCountryCode = $country->getData('iso3_code');

        $shipData['f_name'] = $shippingAddress->getFirstname();
        $shipData['l_name'] = $shippingAddress->getLastname();
        $shipData['street_address'] = $shipAddr;
        $shipData['Country'] = $threeDigitCountryCode;
        $shipData['State'] = $shippingAddress->getRegion();
        $shipData['City'] = $shippingAddress->getCity();
        $shipData['Zip_code'] = $shippingAddress->getPostcode();
        $shipData['phone_number'] = $shippingAddress->getTelephone();
        $shipData["vat_no"] = "";

        $salesData['shipping_address'] = $shipData;

        if( $addressDiff ) {
            $Bstreet = $billingAddress->getStreet();

            $billAddr = $Bstreet[0];
            if (array_key_exists(1,$Bstreet)){
                $billAddr .= " ".$Bstreet[1];
            }

            $countryCode = $billingAddress->getCountryId();
            $country = $this->countryFactory->create()->loadByCode($countryCode);
            $threeDigitCountryCode = $country->getData('iso3_code');

            $billData['f_name'] = $billingAddress->getFirstname();
            $billData['l_name'] = $billingAddress->getLastname();
            $billData['street_address'] = $billAddr;
            $billData['Country'] = $threeDigitCountryCode;
            $billData['State'] = $billingAddress->getRegion();
            $billData['City'] = $billingAddress->getCity();
            $billData['Zip_code'] = $billingAddress->getPostcode();
            $billData['phone_number'] = $billingAddress->getTelephone();
            $billData["vat_no"] = "";

            $salesData['billing_address'] = $billData;
        }else{
            $salesData['billing_address'] = $shipData;
        }

        $salesData['Products'] = $itemData;

        $postdata = json_encode($salesData);

        $resp = $this->dataHelper->setSalesOrder($postdata);

        $autoInv = $this->dataHelper->getAutoInvoice();
        $autoShip = $this->dataHelper->getAutoShip();

        $sellers = array_unique($seller_id);
        foreach($sellers as $sellerId){
            $sellerInfo = array();

            $mpOrderCollection = $this->MpOrdersModel->create()
                    ->getCollection()
                    ->addFieldToFilter(
                        'order_id',
                        $orderId
                    )
                    ->addFieldToFilter(
                        'seller_id',
                        $sellerId
                    );

            if ($mpOrderCollection->getSize()) {
                $mpOrder = $mpOrderCollection->getLastItem();

                // Save seller shipping rates for webkul orders
                if(!empty($sellerShipData)){
                    foreach($sellerShipData as $seller => $shipData){
                        if($seller == $sellerId){
                            $mpOrder->setCarrierName($shipData['service_name']);
                            $mpOrder->setShippingCharges($shipData['price']);
                            $mpOrder->save();
                        }
                    }
                }else{
                    $mpOrder->setCarrierName($shipCarrier);
                    $mpOrder->setShippingCharges($shippingAmt);
                    $mpOrder->save();
                }
            }

            $orderData = $mpOrderCollection->getData()[0];

            $sellerInfo['seller_id'] = $sellerId;
            $sellerInfo['seller_order_status'] = $orderData['order_status'];
            $sellerInfo['payment_status'] = $payment_status;

            $sellerData[] = $sellerInfo;

            if($autoInv == 1){
                $isCpture = 1;
                try {
                    $this->doInvoiceExecution($order,$sellerId,$isCpture);
                } catch (\Exception $exception) {
                    $this->logger->error($exception->getMessage());
                }
            }
        }

        $salesColl = $this->saleslistFactory->create()
                        ->getCollection()
                        ->addFieldToFilter(
                            'order_id',
                            $orderId
                        );

        $totalCommission = 0;
        $clubCommRate = 0;
        $clubCommission = 0;
        $lixReward = 0;

        $lixServiceRate = $this->dataHelper->getLixCharge();

        foreach ($salesColl as $mpSales) {
            $totalCommission += $mpSales->getTotalCommission();
        }

        if($clubId){
            $clubCommRate = $this->dataHelper->getSportsClubComm();
            $clubCommission = $order->getSubtotal() * ($clubCommRate/100);

            $order->setClubCommission($clubCommission);
            $order->setClubName($clubId);
            $order->save();
        }

        if(!$guestCustomer){
            if($orderCnt == 1){
                $lixRewardRate = $this->dataLixHelper->getItemReward();

            }else{
                $lixRewardRate = $this->dataLixHelper->getItemRewardSecond();

            }
        }else{
            $lixRewardRate = $this->dataLixHelper->getItemRewardSecond();
            // $lixServCharge = $order->getSubtotal() * ($lixServiceRate/100);
            // $lixReward = $order->getSubtotal() * ($lixRewardRate/100);
        }

        $this->_messageManager->addSuccessMessage(__("Congratulations! You have earned %1 LIX Rewards.", 0));
    }

    public function getUserInfo($id, $order)
    {
        return $this->userInfoService->get(
            (int) $id,
            (int) $order->getId()
        );
    }

    public function doInvoiceExecution($order,$sellerId,$isCpture)
    {
        $orderId = $order->getId();
        $invoiceId = '';

        $paymentCode = '';
        if ($order->getPayment()) {
            $paymentCode = $order->getPayment()->getMethod();
        }

        $itemsData = $this->getCurrentSellerItemsData(
            $orderId,
            $sellerId,
            $paymentCode
        );

        //$data['send_email'] = 1;
        $items = $itemsData['items'];
        $currencyRate = $itemsData['currencyRate'];
        $codCharges = $itemsData['codCharges'];
        $tax = $itemsData['tax'];
        $couponAmount = $itemsData['couponAmount'];
        //$shippingAmount = $itemsData['shippingAmount'];
        $shippingAmount = $order->getShippingAmount();

        $invoice = $this->objectManager->create(
                        \Magento\Sales\Model\Service\InvoiceService::class
                    )->prepareInvoice($order, $items);

        // this code should be get removed and invoice is got genrated for this order but condition checked wrongly. after one commit this should be get removed after testing.
       /* if (!$invoice->getTotalQty()) {
            throw new \Magento\Framework\Exception\LocalizedException(
                        __("The invoice can't be created without products. Add products and try again.")
            );
        }*/

        $currentCouponAmount = $currencyRate * $couponAmount;
        $currentShippingAmount = $currencyRate * $shippingAmount;
        $currentTaxAmount = $currencyRate * $tax;
        $currentCodcharges = $currencyRate * $codCharges;

        $invoice->setBaseDiscountAmount(-$couponAmount);
        $invoice->setDiscountAmount(-$currentCouponAmount);
        $invoice->setShippingAmount($currentShippingAmount);
        $invoice->setBaseShippingInclTax($shippingAmount);
        $invoice->setBaseShippingAmount($shippingAmount);
        if ($paymentCode == 'mpcashondelivery') {
            $invoice->setMpcashondelivery($currentCodcharges);
            $invoice->setBaseMpcashondelivery($codCharges);
        }
        $invoice->setGrandTotal(
            $invoice->getSubtotal() +
            $currentShippingAmount +
            $currentCodcharges +
            $currentTaxAmount -
            $currentCouponAmount
        );
        $invoice->setBaseGrandTotal(
            $invoice->getBaseSubtotal() +
            $shippingAmount +
            $codCharges +
            $tax -
            $couponAmount
        );

        if($this->_coreRegistry->registry('current_invoice')){
            $this->_coreRegistry->unregister('current_invoice');
        }

        $this->_coreRegistry->register('current_invoice', $invoice);
        if (!$invoice) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('We can\'t save the invoice right now.')
            );
        }
        if ($isCpture) {
            $invoice->setRequestedCaptureCase(\Magento\Sales\Model\Order\Invoice::CAPTURE_ONLINE);
        }

        if (!empty($data['comment_text'])) {
            $invoice->addComment(
                    $data['comment_text'],
                    isset($data['comment_customer_notify']),
                    isset($data['is_visible_on_front'])
            );

            $invoice->setCustomerNote($data['comment_text']);
            $invoice->setCustomerNoteNotify(isset($data['comment_customer_notify']));
        }

        $invoice->register();

        $invoice->getOrder()->setCustomerNoteNotify(!empty($data['send_email']));
        $invoice->getOrder()->setIsInProcess(true);

        $transactionSave = $this->objectManager->create(
                            \Magento\Framework\DB\Transaction::class
                        )->addObject(
                            $invoice
                        )->addObject(
                            $invoice->getOrder()
                        );
        try {
            $transactionSave->save();
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage());
        }

        $invoiceId = $invoice->getId();

        $this->_invoiceSender->send($invoice);

                    /*update mpcod table records*/
                if ($invoiceId != '') {
                    if ($paymentCode == 'mpcashondelivery') {
                        $saleslistColl = $this->saleslistFactory->create()
                        ->getCollection()
                        ->addFieldToFilter(
                            'order_id',
                            $orderId
                        )
                        ->addFieldToFilter(
                            'seller_id',
                            $sellerId
                        );
                        foreach ($saleslistColl as $saleslist) {
                            $saleslist->setCollectCodStatus(1);
                            $saleslist->save();
                        }
                    }

                    $trackingcol1 = $this->MpOrdersModel->create()
                    ->getCollection()
                    ->addFieldToFilter(
                        'order_id',
                        $orderId
                    )
                    ->addFieldToFilter(
                        'seller_id',
                        $sellerId
                    );
                    foreach ($trackingcol1 as $row) {
                        $invoiceIds = explode(',', $row->getInvoiceId());
                        array_push($invoiceIds, $invoiceId);
                        $row->setInvoiceId(implode(',', $invoiceIds));
                        if ($row->getShipmentId()) {
                            $row->setOrderStatus('complete');
                        } else {
                            $row->setOrderStatus('processing');
                        }
                        $row->save();
                    }
                }
        return $invoiceId;
    }

    public function getCurrentSellerItemsData($orderId, $sellerId, $paymentCode)
    {
        // calculate charges for ordered items for current seller
        $codCharges = 0;
        $couponAmount = 0;
        $tax = 0;
        $currencyRate = 1;
        $sellerItemsToInvoice = [];
        $collection = $this->saleslistFactory->create()
                ->getCollection()
                ->addFieldToFilter(
                    'main_table.order_id',
                    ['eq' => $orderId]
                )
                ->addFieldToFilter(
                    'main_table.seller_id',
                    ['eq' => $sellerId]
                )
                ->getSellerOrderCollection();
        foreach ($collection as $saleproduct) {
            $orderItemId = $saleproduct->getOrderItemId();
            $orderedQty = $saleproduct->getQtyOrdered();
            $qtyToInvoice = $orderedQty - $saleproduct->getQtyInvoiced();
            $sellerItemsToInvoice[$orderItemId] = $qtyToInvoice;
            $currencyRate = $saleproduct->getCurrencyRate();
            $appliedTax = $saleproduct->getTotalTax() / $orderedQty;
            $tax = $tax + ($appliedTax * $qtyToInvoice);
            if ($saleproduct->getIsCoupon()) {
                $appliedAmount = $saleproduct->getAppliedCouponAmount() / $orderedQty;
                $couponAmount = $couponAmount + ($appliedAmount * $qtyToInvoice);
            }
        }

        // calculate shipment for the seller order if applied
        $shippingAmount = 0;
        $data = [
            'items' => $sellerItemsToInvoice,
            'currencyRate' => $currencyRate,
            'codCharges' => $codCharges,
            'tax' => $tax,
            'couponAmount' => $couponAmount,
            'shippingAmount' => $shippingAmount
        ];
        return $data;
    }

    private function processShipment(
        $orderId,
        $sellerId,
        $trackingid,
        $carrier,
        $order,
        $trackingData,
        $shippingLabel
    ) {
        $items = [];

        //$data['send_email'] = 1;
        $collection = $this->saleslistFactory->create()
                ->getCollection()
                ->addFieldToFilter(
                    'main_table.order_id',
                    ['eq' => $orderId]
                )
                ->addFieldToFilter(
                    'main_table.seller_id',
                    ['eq' => $sellerId]
                )
                ->getSellerOrderCollection();

        foreach ($collection as $saleproduct) {
            $orderItemId = $saleproduct['order_item_id'];
            $orderedQty = $saleproduct->getQtyOrdered();
            $items[$saleproduct['order_item_id']] = $orderedQty;
        }

        $itemsarray = $this->_getShippingItemQtys($order, $items);

        $itemsToShip = $itemsarray['data'];

        if (count($itemsToShip) > 0) {
            $shipment = false;
            $shipmentId = 0;
            if (!empty($paramData['shipment_id'])) {
                $shipmentId = $paramData['shipment_id'];
            }
            if ($shipmentId) {
                $shipment = $this->_shipment->load($shipmentId);
            } elseif ($orderId) {
                if ($order->getForcedDoShipmentWithInvoice()) {
                    $this->_messageManager
                    ->addError(
                        __('Cannot do shipment for the order separately from invoice.')
                    );
                    $this->logger->error(
                        "Cannot do shipment for the order separately from invoice."
                    );
                }
                if (!$order->canShip()) {
                    $this->_messageManager->addError(
                        __('Cannot do shipment for the order.')
                    );
                    $this->logger->error(
                        "Cannot do shipment for the order."
                    );
                }

                $shipment = $this->_prepareShipment(
                    $order,
                    $itemsToShip,
                    $trackingData
                );
                if ($shippingLabel!='') {
                    $shipment->setShippingLabel($shippingLabel);
                }
            }
            if ($shipment) {
                $comment = '';
                $shipment->getOrder()->setCustomerNoteNotify(
                    !empty($data['send_email'])
                );
                $isNeedCreateLabel=!empty($shippingLabel) && $shippingLabel;
                $shipment->getOrder()->setIsInProcess(true);

                $transactionSave = $this->objectManager->create(
                    \Magento\Framework\DB\Transaction::class
                )->addObject(
                    $shipment
                )->addObject(
                    $shipment->getOrder()
                );
                $transactionSave->save();

                $shipmentId = $shipment->getId();

                $sellerCollection = $this->MpOrdersModel->create()
                ->getCollection()
                ->addFieldToFilter(
                    'order_id',
                    ['eq' => $orderId]
                )
                ->addFieldToFilter(
                    'seller_id',
                    ['eq' => $sellerId]
                );
                foreach ($sellerCollection as $row) {
                    if ($shipment->getId() != '') {
                        $shipmentIds = explode(',', $row->getShipmentId());
                        array_push($shipmentIds, $shipment->getId());
                        $row->setShipmentId(implode(',', $shipmentIds));
                        $row->setTrackingNumber($trackingid);
                        $row->setCarrierName($carrier);
                        if ($row->getInvoiceId()) {
                            $row->setOrderStatus('complete');
                        } else {
                            $row->setOrderStatus('processing');
                        }
                        $row->save();
                    }
                }
            }
        }
    }
    protected function _getShippingItemQtys($order, $items)
    {
        $data = [];
        $subtotal = 0;
        $baseSubtotal = 0;
        foreach ($order->getAllItems() as $item) {
            $orderItemId = $item->getItemId();
            if (isset($items[$orderItemId])) {
                $availableQtyToShip = (int) ($item->getQtyOrdered() -
                    $item->getQtyShipped() -
                    $item->getQtyRefunded() -
                    $item->getQtyCanceled()
                );

                if ($items[$orderItemId] <= $availableQtyToShip) {
                    $data[$orderItemId] = $items[$orderItemId];
                } else {
                    $data[$orderItemId] = $availableQtyToShip;
                }

                $_item = $item;

                $subtotal += $_item->getRowTotal();
                $baseSubtotal += $_item->getBaseRowTotal();
            } else {
                if (!$item->getParentItemId()) {
                    $data[$item->getItemId()] = 0;
                }
            }
        }

        return ['data' => $data,'subtotal' => $subtotal,'baseSubtotal' => $baseSubtotal];
    }
    protected function _prepareShipment($order, $items, $trackingData)
    {
        $shipment = $this->_shipmentFactory->create(
            $order,
            $items,
            $trackingData
        );

        if (!$shipment->getTotalQty()) {
            return false;
        }

        return $shipment->register();
    }
}
