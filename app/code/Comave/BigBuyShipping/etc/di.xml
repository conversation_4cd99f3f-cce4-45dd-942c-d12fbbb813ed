<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="bigbuy_shipping_test" xsi:type="object">Comave\BigBuyShipping\Console\Command\BigBuyShippingTest</item>
            </argument>
        </arguments>
    </type>

    <type name="Coditron\CustomShippingRate\Model\RateProviders\RatePoolProvider">
        <arguments>
            <argument xsi:type="array" name="providers">
                <item name="bigbuy" xsi:type="object">Comave\BigBuyShipping\Model\RateProvider\BigbuyRateFactory</item>
            </argument>
        </arguments>
    </type>
</config>
