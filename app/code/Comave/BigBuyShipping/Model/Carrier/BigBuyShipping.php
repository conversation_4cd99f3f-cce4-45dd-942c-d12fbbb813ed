<?php
declare(strict_types=1);

namespace Comave\BigBuyShipping\Model\Carrier;

use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Quote\Model\Quote\Address\RateResult\Error;
use Magento\Shipping\Model\Rate\Result as RateResult;
use Magento\Shipping\Model\Carrier\AbstractCarrierOnline;
use Magento\Shipping\Model\Carrier\CarrierInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Comave\BigBuy\Model\ConfigProvider;
use Magento\Framework\DataObject;
use Comave\BigBuyShipping\Service\BigBuyShippingService;

/**
 * @TODO - Currently not in use as the Coditron Shipping Rate is used for encompassing all seller methods
 * Maybe we will need it further down the line and as such we only need to add the system and config xmls back
 */
class BigBuyShipping extends AbstractCarrierOnline implements CarrierInterface
{
    public const string CARRIER_CODE = 'bigbuy';
    protected $_code = self::CARRIER_CODE;
    protected LoggerInterface $logger;
    protected ScopeConfigInterface $scopeConfig;
    /** @var string[] Errors placeholder */
    protected array $_errors = [];

    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Framework\Xml\Security $xmlSecurity,
        \Magento\Shipping\Model\Simplexml\ElementFactory $xmlElFactory,
        \Magento\Shipping\Model\Rate\ResultFactory $rateFactory,
        \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory,
        \Magento\Shipping\Model\Tracking\ResultFactory $trackFactory,
        \Magento\Shipping\Model\Tracking\Result\ErrorFactory $trackErrorFactory,
        \Magento\Shipping\Model\Tracking\Result\StatusFactory $trackStatusFactory,
        \Magento\Directory\Model\RegionFactory $regionFactory,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Directory\Model\CurrencyFactory $currencyFactory,
        \Magento\Directory\Helper\Data $directoryData,
        \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry,
        private readonly ConfigProvider $configProvider,
        private readonly BigBuyShippingService $shippingService,
        array $data = []
    ) {
        parent::__construct(
            $scopeConfig,
            $rateErrorFactory,
            $logger,
            $xmlSecurity,
            $xmlElFactory,
            $rateFactory,
            $rateMethodFactory,
            $trackFactory,
            $trackErrorFactory,
            $trackStatusFactory,
            $regionFactory,
            $countryFactory,
            $currencyFactory,
            $directoryData,
            $stockRegistry,
            $data
        );

        $this->logger = $logger;
    }

    /**
     * @param RateRequest $request
     * @return \Magento\Framework\DataObject|RateResult|bool|null
     */
    public function collectRates(RateRequest $request): DataObject|RateResult|bool|null
    {
        if (!$this->canCollectRates()) {
            return $this->getErrorMessage();
        }

        // If method is not active, or BigBuy integration not enabled, return false
        if (!$this->configProvider->isEnabled()) {
            return $this->getErrorMessage('BigBuy integration not enabled');
        }

        // Early return if missing api key, redundant, also checked by ConfigurableApi further
        try {
            $apiKey = $this->configProvider->getApiKey();
        } catch (\Exception $e) {
            return $this->getErrorMessage('BigBuy api key error: ', [$e->getMessage()]);
        }
        if (!$apiKey) {
            return $this->getErrorMessage('BigBuy api key missing or corrupted');
        }

        try {
            $shippingData = $this->getShippingRates($request);
        } catch (ClientExceptionInterface|\Exception $e) {
            $this->_errors[] = 'Exception: ' . $e->getMessage();
            return false;
        }

        if (!$shippingData) {
            return $this->getErrorMessage('BigBuy shipping api returned no rates');
        }

        $result = $this->_rateFactory->create();
        foreach ($shippingData as $methodData) {
            $methodTitle = $methodData['carrier_name'];
            if (!empty($methodData['delivery_time'])) {
                $methodTitle .= sprintf(' (%s)', $methodData['delivery_time']);
            }
            $method = $this->_rateMethodFactory->create();
            $method->setCarrier($this->_code);
            $method->setCarrierTitle('BigBuy Shipping');
            $method->setMethod($methodData['method_code']);
            $method->setMethodTitle($methodTitle);
            $method->setPrice($methodData['cost']);
            $method->setCost($methodData['cost']);
            $result->append($method);
        }

        if (!empty($this->_errors)) {
            foreach ($this->_errors as $error) {
                $this->createErrorResponse($error);
            }
        }

        return $result;
    }

    /**
     * Gets shipping rates from API for order shipping request
     * @param RateRequest $request
     * @return false|array
     * @throws ClientExceptionInterface|\Exception
     */
    private function getShippingRates(RateRequest $request): false|array
    {
        // build payload
        $products = $this->getProductsData($request);
        if (empty($products)) {
            return false;
        }

        return $this->shippingService->fetchShippingRates(
            $products,
            $request->getDestCountryId(),
            $request->getDestPostcode(),
            (bool) $this->getDebugFlag()
        );
    }

    /**
     * Extract products data from Magento shipping rate request and return them as array structured for BigBuy payload
     * @param RateRequest $request
     * @return array
     */
    private function getProductsData(RateRequest $request): array
    {
        $products = [];

        foreach ($request->getAllItems() as $item) {
            // Skip virtual products (downloadable, services), Skip child items (simples, bundled)
            $product = $item->getProduct();
            if ($product->isVirtual() || $product->getParentItem()) {
                continue;
            }

            $products[] = [
                "reference" => $item->getSku(),
                "quantity" => (int) $item->getQty()
            ];
        }

        return $products;
    }

    /**
     * @inheritdoc
     */
    public function getAllowedMethods(): array
    {
        return [$this->_code => $this->getConfigData('name')];
    }

    /**
     * Only for compliance with parent abstract class AbstractCarrierOnline
     * TODO: Might need adjusting according to how placing order shipment creation is implemented
     * @inheirtdoc
     */
    public function requestToShipment($request): DataObject
    {
        return $this->_doShipmentRequest($request);
    }

    /**
     * Only for compliance with parent abstract class AbstractCarrierOnline
     * TODO: Might need adjusting according to how placing order shipment creation is implemented
     * TODO: Recheck if error is properly returned
     * @inheirtdoc
     */
    protected function _doShipmentRequest(DataObject $request): DataObject
    {
        $msg = 'BigBuy Shipping: _doShipmentRequest() called, but shipment creation is not implemented.';
        $this->logger->info($msg);
        return (new \Magento\Framework\DataObject([]))
            ->setErrors([$msg]);
    }

    /**
     * Overloaded to allow specific error message instead of default configured 'specificerrmsg' and debug logging
     * @param string|null $specificMessage
     * @param array $context
     * @return false|Error
     */
    protected function getErrorMessage(?string $specificMessage = null, array $context = []): false|Error
    {
        if ($this->getConfigData('showmethod')) {
            return $this->createErrorResponse($specificMessage);
        } else {
            return false;
        }
    }

    /**
     * Creates and returns error response, also debug logs
     * @param string|null $specificMessage
     * @param array $context
     * @return Error
     */
    protected function createErrorResponse(string $specificMessage = null, array $context = []): Error
    {
        $this->logger->error('BigBuyShipping error: ' . $specificMessage, $context);

        // build error response
        $error = $this->_rateErrorFactory->create();
        $error->setCarrier($this->getCarrierCode());
        $error->setCarrierTitle($this->getConfigData('title'));
        $error->setErrorMessage($specificMessage ?? $this->getConfigData('specificerrmsg'));
        return $error;
    }
}
