<?php

declare(strict_types=1);

namespace Comave\CategoryMatcher\Service;

use Comave\CategoryMatcher\Api\CategoryMatcherServiceInterface;
use Comave\CategoryMatcher\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Service\RequestHandler;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class CategoryMatcher implements CategoryMatcherServiceInterface
{
    private const int MAX_RETRIES = 5;

    /**
     * @param SerializerInterface $serializer
     * @param LoggerInterface $logger
     * @param RequestHandler $requestHandler
     * @param ConfigProvider $configProvider
     * @param ConfigurableApiInterfaceFactory $configurableApiFactory
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigProvider $configProvider,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
    ) {
    }

    /**
     * @param array{array{sku: string, word: string}} $categories
     * @param string $lang
     * @return array{sku: string, categoryIds: int[], similarity: float}
     * @throws \Throwable
     */
    public function matchCategories(array $categories, string $lang = 'en'): array
    {
        if (!$this->configProvider->isEnabled()) {
            $this->logger->info(
                '[Comave.CategoryMatcher] Not enabled, returning'
            );

            return [];
        }

        if (empty($this->configProvider->getEndpoint())) {
            $this->logger->info(
                '[Comave.CategoryMatcher] No endpoint specified, returning'
            );

            return [];
        }

        $result = [];

        try {
            foreach (array_chunk($categories, 25) as $chunkedCategories) {
                $this->initMatching($chunkedCategories, $lang);
            }

            foreach ($categories as $categoryArr) {
                $result += $this->processCategory($categoryArr);
            }
        } catch (\Throwable $e) {
            $this->logger->error(
                '[Comave.CategoryMatcher] There was an error with the request',
                [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTrace(),
                ]
            );
            throw $e;
        }

        return $result;
    }

    /**
     * @param array $categoryArr
     * @return array
     */
    private function processCategory(array $categoryArr): array
    {
        $result = [];
        $configurableApi = $this->configurableApiFactory->create([
            'method' => 'GET',
            'endpoint' => $this->configProvider->getEndpoint().'/one/'.$categoryArr['sku'],
        ]);

        $try = 0;
        while (empty($result) && $try++ < self::MAX_RETRIES) {
            try {
                $response = $this->requestHandler->handleRequest($configurableApi);
                if (!$response->hasError()) {
                    $decodedResponse = $this->serializer->unserialize(
                        $response->getResult()->getBody()->getContents()
                    );

                    if (isset($decodedResponse['result'])) {
                        $magentoCategoryIds = current($decodedResponse['result']);
                        $result[$categoryArr['sku']] = [
                            'categoryIds' => $magentoCategoryIds['magentoIds'],
                            'accuracy' => $magentoCategoryIds['similarity'],
                        ];

                        $this->logger->info(
                            sprintf('[Comave.CategoryMatcher] (Try #%s) Response received', $try),
                            [
                                'categoryMatch' => $categoryArr,
                                'matched' => $magentoCategoryIds,
                                'highest' => $magentoCategoryIds,
                            ]
                        );
                    }
                }
            } catch (ClientExceptionInterface $exception) {
                $this->logger->critical(
                    sprintf("Cannot get category matching for SKU %s", $categoryArr['sku']),
                    [
                        'categoryMatch' => $categoryArr,
                        'exception' => $exception->getMessage(),
                    ]
                );
            }
        }

        return $result;
    }

    private function initMatching(array $chunkedCategories, string $lang): void
    {
        $sendParam = $this->serializer->serialize([
            'items' => $chunkedCategories
        ]);
        $endpointUrl = $this->configProvider->getEndpoint() . '?language=' . $lang;
        $configurableApi = $this->configurableApiFactory->create([
            'method' => 'POST',
            'endpoint' => $endpointUrl,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'params' => $sendParam,
        ]);

        $this->logger->info(
            '[Comave.CategoryMatcher] Performing request',
            [
                'endpoint' => $endpointUrl,
                'params' => $sendParam,
            ]
        );

        $response = $this->requestHandler->handleRequest($configurableApi);
        $resultContents = $response->getResult()->getBody()->getContents();

        if ($response->hasError()) {
            throw new LocalizedException(__($resultContents));
        }
    }
}
