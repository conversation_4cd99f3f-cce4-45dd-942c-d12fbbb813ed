<?php

declare(strict_types=1);

namespace Comave\BigBuy\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class UpdateAttributes implements DataPatchInterface
{
    /**
     * @param EavSetupFactory $eavSetupFactory
     * @param ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly ModuleDataSetupInterface $moduleDataSetup
    ) {
    }

    /**
     * @return string[]
     */
    public static function getDependencies(): array
    {
        return [
            InstallAttributes::class
        ];
    }

    /**
     * @return string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return $this
     */
    public function apply(): self
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = \Magento\Catalog\Model\Product::ENTITY;

        foreach (['ean', 'barcode'] as $attributeCode) {
            $eavSetup->updateAttribute(
                $entityTypeId,
                $attributeCode,
                [
                    'is_used_in_grid'      => 1,
                    'is_visible_in_grid'   => 1,
                    'is_filterable_in_grid'=> 1
                ]
            );
        }

        return $this;
    }
}
