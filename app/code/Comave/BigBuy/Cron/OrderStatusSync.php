<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Cron;

use Comave\BigBuy\Service\Order\Status\Synchroniser;

class OrderStatusSync
{
    /**
     * @param \Comave\BigBuy\Service\Order\Status\Synchroniser
     */
    public function __construct(
        private readonly Synchroniser $orderStatusSynchroniser,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $this->orderStatusSynchroniser->execute();
    }
}
