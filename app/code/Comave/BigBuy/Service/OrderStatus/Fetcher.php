<?php

declare(strict_types=1);

namespace Comave\BigBuy\Service\OrderStatus;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\SellerApi\Service\RequestHandler;
use Psr\Log\LoggerInterface;
use Laminas\Http\Request;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Psr\Http\Client\ClientExceptionInterface;

class Fetcher
{
    public function __construct(
        private readonly ConfigProvider $configProvider,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return string
     * @throws ClientExceptionInterface
     */
    public function fetch(): string
    {
        if (!$this->configProvider->isOrderStatusSyncEnable()) {
            $this->logger->warning('BigBuy OrderStatus Sync process is disabled');
            return '';
        }

        if (empty($this->configProvider->getApiEndpoint())) {
            $this->logger->warning('BigBuy API endpoint is not configured');
            return '';
        }

        $orderStatusSyncEndpoint = $this->configProvider->getOrderStatusSyncEndpoint();
        if (filter_var($orderStatusSyncEndpoint, FILTER_VALIDATE_URL) === false) {
            $this->logger->warning('BigBuy Order Status Sync endpoint is invalid or not configured', [
                'resolved_endpoint' => $orderStatusSyncEndpoint,
            ]);
            return '';
        }

        return $this->fetchData();
    }

    /**
     * @return string Raw JSON, or empty string on error
     * @throws ClientExceptionInterface
     */
    private function fetchData(): string
    {
        try {
            $configurableApi = $this->configurableApiFactory->create([
                'method' => Request::METHOD_GET,
                'endpoint' => $this->configProvider->getOrderStatusSyncEndpoint(),
                'headers' => $this->getApiHeaders(),
            ]);

            $response = $this->requestHandler->handleRequest($configurableApi);
            if ($response->hasError()) {
                $this->logger->error('BigBuy API responded with error', [
                    'response' => $response->getResult()->getBody()->getContents()
                ]);
                return '';
            }

            return $response->getResult()->getBody()->getContents();
        } catch (ClientExceptionInterface $exception) {
            $this->logger->critical(
                'Failed to fetch BigBuy order statuses from API',
                ['reason' => $exception->getMessage()]
            );
            return '';
        } catch (\Throwable $exception) {
            $this->logger->critical(
                'Unexpected error during BigBuy order status fetch',
                ['exception' => $exception]
            );
            return '';
        }
    }

    /**
     * @return array
     * @throws \Exception
     */
    private function getApiHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
        ];
    }
}
