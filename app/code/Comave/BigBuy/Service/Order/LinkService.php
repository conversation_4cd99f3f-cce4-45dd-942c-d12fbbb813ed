<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\BigBuy\Model\OrderLinkUiManager;
use Psr\Log\LoggerInterface;

class LinkService
{
    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\BigBuy\Model\OrderLinkUiManager $orderLinkUiManager
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly OrderLinkUiManager $orderLinkUiManager,
    ) {
    }

    /**
     * @param int $orderId
     * @param string $bigBuyOrderId
     * @return void
     */
    public function createLink(int $orderId, string $bigBuyOrderId): void
    {
        if (empty($bigBuyOrderId)) {
            $this->logger->critical('BigBuy Order ID cannot be empty when creating a link.', [
                'order_id' => $orderId,
                'bigbuy_order_id' => $bigBuyOrderId,
            ]);

            return;
        }

        $orderLink = $this->orderLinkUiManager->getByOrderId($orderId);
        $orderLink->setOrderId($orderId)
            ->setBigBuyOrderId($bigBuyOrderId);
        $this->orderLinkUiManager->save($orderLink);
    }

    /**
     * @return array
     */
    public function getOrderLinks(): array
    {
        return $this->orderLinkUiManager->getList();
    }
}
