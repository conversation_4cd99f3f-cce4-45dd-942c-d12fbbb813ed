<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order\Status;

use Comave\BigBuy\Service\Order\Fetcher;
use Comave\BigBuy\Service\Order\LinkService;
use Comave\MapOrderStatuses\Model\ConfigProvider;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\MapOrderStatuses\Service\OrderStatuses;
use Psr\Log\LoggerInterface;

class Synchroniser
{
    /**
     * @var \Symfony\Component\Console\Output\OutputInterface|null
     */
    private ?OutputInterface $output = null;

    /**
     * @param \Symfony\Component\Console\Helper\ProgressBarFactory $progressBarFactory
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Comave\BigBuy\Service\Order\LinkService $orderLinkService
     * @param \Comave\BigBuy\Service\Order\Fetcher $orderFetcherService
     */
    public function __construct(
        private readonly ConfigProvider $orderStatusConfigProvider,
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly LinkService $orderLinkService,
        private readonly Fetcher $orderFetcherService,
        private readonly OrderStatuses $orderStatuses,
        private readonly LoggerInterface $logger,

    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        if ($this->hasOutput()) {
            $this->output->writeln(
                sprintf(
                    "<info>%s</info>",
                    __('Collecting BigBuy orders in the system ...')
                )
            );
        }
        $orderLinks = $this->orderLinkService->getOrderLinks();
        if ($this->hasOutput()) {
            $this->output->writeln(
                sprintf(
                    "<info>%s</info>",
                    __('Synchronising BigBuy orders statuses ...')
                )
            );
            $this->getOutput()->writeln("");
            $progressBar = $this->getProgressBar(count($orderLinks));
            $progressBar->start();
            $progressBar->display();
        }
        foreach ($orderLinks as $orderLink) {
            $order = $this->orderRepository->get((int)$orderLink->getOrderId());
            if (!$order->isCanceled() &&
                !$order->isPaymentReview() &&
                $order->getState() !== Order::STATE_COMPLETE &&
                $order->getState() !== Order::STATE_CLOSED
            ) {
                $bigbuyOrder = $this->orderFetcherService->fetch((string)$orderLink->getBigBuyOrderId());
                if (!empty($bigbuyOrder)) {
                    $this->updateOrderStatus($order, $bigbuyOrder);
                }
            }
            if ($this->hasOutput() && !empty($progressBar)) {
                $progressBar->advance();
            }
        }
        if ($this->hasOutput() && !empty($progressBar)) {
            $progressBar->finish();
            $this->getOutput()->writeln("\n");
            $this->getOutput()->writeln(
                sprintf(
                    '<info>%s</info>',
                    __('BigBuy orders statuses have been synchronised successfully')
                )
            );
        }
    }

    /**
     * @param null|OutputInterface $output
     */
    public
    function setOutput(
        ?OutputInterface $output
    ): void {
        $this->output = $output;
    }

    /**
     * @return \Symfony\Component\Console\Output\OutputInterface|null
     */
    private
    function getOutput(): ?OutputInterface
    {
        return $this->output;
    }

    /**
     * @param int $dimension
     * @return \Symfony\Component\Console\Helper\ProgressBar|null
     */
    private
    function getProgressBar(
        int $dimension = 0
    ): ?ProgressBar {
        $progressBar = null;
        if ($this->hasOutput()) {
            $progressBar = $this->progressBarFactory->create([
                'output' => $this->getOutput(),
                'max' => $dimension,
            ]);
            $progressBar->setBarCharacter('<fg=green>⚬</>');
            $progressBar->setEmptyBarCharacter("<fg=red>⚬</>");
            $progressBar->setProgressCharacter("<fg=green>➤</>");
        }

        return $progressBar;
    }

    /**
     * @return bool
     */
    private
    function hasOutput(): bool
    {
        return !is_null($this->output);
    }

    /**
     * @param int $orderId
     * @param array $bigbuyOrder
     * @return void
     */
    private function updateOrderStatus(
        Order $order,
        array $bigbuyOrder
    ): void {
        $status = $this->getOrderStatus($bigbuyOrder['status']);
        if (empty($status)) {
            return;
        }

        if ($this->orderStatuses->isStatusActive($status) === false
            || $this->orderStatusConfigProvider->isPredefinedStatusMapped($status) === false
        ) {
            $this->logger->warning(
                'Skipped status update: Status is inactive or not permitted.',
                [
                    'order_id' => $order->getId(),
                    'status' => $status,
                ]
            );
            return;
        }

        $order->setStatus($status);
        if ($order->canComment()) {
            $order->addCommentToStatusHistory('Status Synchronised from BigBuy system.', $status);
        }

        $this->orderRepository->save($order);
    }

    /**
     * @param string $status
     * @return string
     */
    private function getOrderStatus(string $status): string
    {
        return $this->orderStatusConfigProvider->getStatus(ConfigProvider::BIGBUY_PROVIDER, $status);
    }
}
