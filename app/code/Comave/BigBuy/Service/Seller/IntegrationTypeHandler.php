<?php
declare(strict_types=1);

namespace Comave\BigBuy\Service\Seller;

use Comave\BigBuy\Service\Order\SynchroniseService;
use Comave\SellerApi\Service\SellerIdentity;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class IntegrationTypeHandler
{
    /**
     * @param SellerIdentity $sellerIdentity
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly SellerIdentity $sellerIdentity,
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param array $products
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(array $products): void
    {
        if (!empty($products)) {
            $data = [];
            $sellerId = $this->sellerIdentity->identify(SynchroniseService::BIG_BUY_IDENTIFIER);
            if (!empty($sellerId)) {
                foreach ($products as $product) {
                    if (!($product instanceof ProductInterface)) {
                        $productId = (int)$product;
                        $productSellerId = (int)$sellerId;
                    } else {
                        $productId = (int)$product->getId();
                        $assignSellerData = $product->getAssignSeller();
                        $productSellerId = isset($assignSellerData['seller_id']) && is_numeric($assignSellerData['seller_id'])
                            ? (int) $assignSellerData['seller_id']
                            : 0;
                    }

                    if ($sellerId === $productSellerId) {
                        $data[] = [
                            'product_id' => $productId,
                            'seller_id' => $sellerId,
                            'integration_type' => SynchroniseService::BIG_BUY_IDENTIFIER,
                        ];
                    }
                }
            }
            if (!empty($data)) {
                $this->setIntegrationType($data);
            }
        }
    }

    /**
     * @param array $data
     * @return void
     */
    private function setIntegrationType(array $data): void
    {
        $connection = $this->resourceConnection->getConnection();
        $connection->beginTransaction();
        try {
            $connection->insertOnDuplicate(
                $connection->getTableName('comave_marketplace_products_custom_api'),
                $data,
                [
                    'integration_type',
                ]
            );
            $connection->commit();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            $connection->rollBack();
        }
    }
}
