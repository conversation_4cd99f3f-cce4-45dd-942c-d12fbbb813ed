<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="coditron_cartrule_comaveseller_coupon" resource="default" engine="innodb" comment="coditron_cartrule_comaveseller_coupon Table">
		<column xsi:type="int" name="comaveseller_coupon_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="comaveseller_coupon_id"/>
		</constraint>
		<column name="rule_id" xsi:type="int" unsigned="true" nullable="false" comment="rule_id" identity="false"/>
		<column name="rule_status" nullable="true" xsi:type="int" comment="rule_status" identity="false"/>
		<column name="rule_name" nullable="true" xsi:type="varchar" comment="rule_name" length="255"/>
		<column xsi:type="int" name="seller_name" padding="10" unsigned="true" nullable="false" identity="false" comment="seller id"/>
		<column name="coupon_code" nullable="true" xsi:type="varchar" comment="rule_name" />
		<column xsi:type="varchar" name="coupon_type" nullable="true" length="32" comment="Simple Action"/>
        <column xsi:type="decimal" name="coupon_value" precision="10" scale="2" unsigned="true" nullable="false" comment="coupon value"/>
        <column xsi:type="int" name="coupon_step" unsigned="true" nullable="false" identity="false" default="0" comment="Discount Step"/>
        <column xsi:type="timestamp" name="expire_at" on_update="false" nullable="true" comment="expireat"/>
        <column xsi:type="boolean" name="apply_on_last" nullable="false" comment="Apply On Last"/>
        <column name="customer_group_ids" xsi:type="varchar" length="255" nullable="true" comment="Customer Group IDs (comma-separated)"/>
        <column name="website_ids" xsi:type="varchar" length="255" nullable="true" comment="Website IDs (comma-separated)"/>
	</table>
	<table name="quote">
		 <column name="custom_data" nullable="true" xsi:type="decimal" precision="12" scale="2" comment="Custom Discount Data" />
	</table>
</schema>
