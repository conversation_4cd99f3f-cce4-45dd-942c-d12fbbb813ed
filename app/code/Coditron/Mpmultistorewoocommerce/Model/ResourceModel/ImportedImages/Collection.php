<?php
/**
 * Webkul Software.
 *
 * @category Webkul
 * @package Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR>
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license https://store.webkul.com/license.html
 */


namespace Coditron\Mpmultistorewoocommerce\Model\ResourceModel\ImportedImages;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{

    /**
     * @var string
     */
    protected $_idFieldName = 'entity_id';

    /**
     * Initialize resource model
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init(
            \Webkul\MpMultiShopifyStoreMageConnect\Model\ImportedImages::class,
            \Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\ImportedImages::class
        );
        $this->_map['fields']['entity_id'] = 'main_table.entity_id';
    }
}
