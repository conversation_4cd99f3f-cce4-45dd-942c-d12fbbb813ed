<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Api\Data;

interface ShipTableRatesSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get ShipTableRates list items
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface[]
     */
    public function getItems();

    /**
     * Set ShipTableRates list itemsapp/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesSearchResultsInterface.php
     * @param \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

