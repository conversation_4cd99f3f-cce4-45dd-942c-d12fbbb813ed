<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Co<PERSON>ron\CustomShippingRate\Api;

use Magento\Framework\Api\SearchCriteriaInterface;
use Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface;
use Coditron\CustomShippingRate\Api\Data\ShipTableRatesSearchResultsInterface;

interface ShipTableRatesRepositoryInterface
{

    /**
     * Save ShipTableRates
     * @param \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface $shipTableRates
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface $shipTableRates
    ): ShipTableRatesInterface;

    /**
     * Retrieve ShipTableRates
     * @param int $shipTableRatesId
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get(int $shipTableRatesId): ShipTableRatesInterface;

    /**
     * Retrieve ShipTableRates matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    ): ShipTableRatesSearchResultsInterface;

    /**
     * Delete ShipTableRates
     * @param \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface $shipTableRates
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface $shipTableRates
    ): bool;

    /**
     * Delete ShipTableRates by ID
     * @param int $shipTableRatesId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById(int $shipTableRatesId): bool;
}

