<?php

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Api;

use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Quote\Model\Quote\Address\RateRequest;

interface ShippingRateProviderInterface
{
    /**
     * @param RateRequest $request
     * @param string $sellerIdentifier
     * @param string $sellerId
     * @return array<ShipTableRates[]>
     */
    public function get(RateRequest $request, string $sellerIdentifier, string $sellerId): array;
}
