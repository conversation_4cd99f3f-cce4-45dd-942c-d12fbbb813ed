<?php
declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\InvalidEmailOrPasswordException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;

/**
 * Seller change password controller
 *
 * Allows logged-in sellers to change their password by providing current password
 * and new password, following standard Magento change password patterns.
 */
class ChangePassword extends Action implements HttpPostActionInterface
{
    public function __construct(
        Context $context,
        private AccountManagementInterface $accountManagement,
        private CustomerRepositoryInterface $customerRepository,
        private CustomerSession $customerSession,
        private FormKeyValidator $formKeyValidator
    ) {
        parent::__construct($context);
    }

    public function execute()
    {
        if (!$this->customerSession->isLoggedIn()) {
            $this->messageManager->addErrorMessage(__('You must be logged in to change your password.'));
            return $this->resultRedirectFactory->create()->setPath('customer/account/login');
        }

        if (!$this->formKeyValidator->validate($this->getRequest())) {
            $this->messageManager->addErrorMessage(__('Invalid form key. Please try again.'));
            return $this->resultRedirectFactory->create()->setPath('marketplace/account/editprofile');
        }

        $currentPassword = $this->getRequest()->getParam('current_password');
        $newPassword = $this->getRequest()->getParam('password');
        $confirmPassword = $this->getRequest()->getParam('password_confirmation');

        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $this->messageManager->addErrorMessage(__('All password fields are required.'));
            return $this->resultRedirectFactory->create()->setPath('marketplace/account/editprofile');
        }

        if ($newPassword !== $confirmPassword) {
            $this->messageManager->addErrorMessage(__('New password and confirmation do not match.'));
            return $this->resultRedirectFactory->create()->setPath('marketplace/account/editprofile');
        }

        try {
            $customerId = (int) $this->customerSession->getId();
            $customer = $this->customerRepository->getById($customerId);

            // Change password using Magento's core functionality
            $this->accountManagement->changePasswordById($customerId, $currentPassword, $newPassword);

            $this->messageManager->addSuccessMessage(__('You have successfully changed your password.'));

        } catch (InvalidEmailOrPasswordException $e) {
            $this->messageManager->addErrorMessage(__('The current password you entered is incorrect.'));
        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Unable to find your customer account.'));
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('Something went wrong while changing your password. Please try again.'));
        }

        return $this->resultRedirectFactory->create()
            ->setPath('marketplace/account/editprofile');
    }
}
