<?php
declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\InvalidEmailOrPasswordException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Magento\Framework\Controller\Result\JsonFactory;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

/**
 * Seller change password controller
 * 
 * Allows logged-in sellers to change their password via AJAX
 * within the marketplace seller dashboard context.
 */
class ChangePassword extends Action implements HttpPostActionInterface
{
    public function __construct(
        Context $context,
        private AccountManagementInterface $accountManagement,
        private CustomerSession $customerSession,
        private FormKeyValidator $formKeyValidator,
        private JsonFactory $resultJsonFactory,
        private MarketplaceHelper $marketplaceHelper
    ) {
        parent::__construct($context);
    }

    public function execute()
    {
        $resultJson = $this->resultJsonFactory->create();
        
        if (!$this->customerSession->isLoggedIn()) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('You must be logged in to change your password.')
            ]);
        }

        if (!$this->formKeyValidator->validate($this->getRequest())) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('Invalid form key. Please refresh the page and try again.')
            ]);
        }

        $currentPassword = $this->getRequest()->getParam('current_password');
        $newPassword = $this->getRequest()->getParam('new_password');
        $confirmPassword = $this->getRequest()->getParam('confirm_password');

        // Validate required fields
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('All password fields are required.')
            ]);
        }

        // Validate password confirmation
        if ($newPassword !== $confirmPassword) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('New password and confirmation do not match.')
            ]);
        }

        // Validate password length
        if (strlen($newPassword) < 8) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('Password must be at least 8 characters long.')
            ]);
        }

        try {
            $customerId = (int) $this->customerSession->getId();
            
            // Change password using Magento's core functionality
            $this->accountManagement->changePasswordById($customerId, $currentPassword, $newPassword);

            // Log the password change
            $this->marketplaceHelper->logDataInLogger(
                "Seller password changed successfully for customer ID: " . $customerId
            );

            return $resultJson->setData([
                'success' => true,
                'message' => __('You have successfully changed your password.')
            ]);

        } catch (InvalidEmailOrPasswordException $e) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('The current password you entered is incorrect.')
            ]);
        } catch (NoSuchEntityException $e) {
            return $resultJson->setData([
                'success' => false,
                'message' => __('Unable to find your customer account.')
            ]);
        } catch (LocalizedException $e) {
            return $resultJson->setData([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            $this->marketplaceHelper->logDataInLogger(
                "Password change error for customer ID " . $this->customerSession->getId() . ": " . $e->getMessage()
            );
            
            return $resultJson->setData([
                'success' => false,
                'message' => __('Something went wrong while changing your password. Please try again.')
            ]);
        }
    }
}
